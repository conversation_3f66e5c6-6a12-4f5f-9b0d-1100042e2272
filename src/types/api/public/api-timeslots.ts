export type TimeSlot = {
  id: number;
  start_time: Date;
  end_time: Date;
  duration: number;
  fee?: number;
  currency?: string;
  clinic_id: number;
  clinic: {
    id: number;
    clinic_name: string;
    address: string;
    city: {
      id: number;
      city_name: string;
      state_name: string;
    };
  };
  doctor_name?: string | null;
};

export type DateSlotGroup = {
  date: string;
  available_slots: TimeSlot[];
};

export type TimeSlotsResponse = {
  date_range: {
    start_date: string;
    end_date: string;
  };
  total_slots: number;
  date_slots: DateSlotGroup[];
};