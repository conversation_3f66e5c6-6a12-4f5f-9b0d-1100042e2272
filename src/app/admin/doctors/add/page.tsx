"use client";
import React, { useState, useEffect, useMemo } from "react";
import { useRouter } from "next/navigation";
import { usePageHeader } from "@/contexts/PageHeaderContext";
import { <PERSON><PERSON> } from "@/components/ShadcnUI/button";
import { Input } from "@/components/ShadcnUI/input";
import { Label } from "@/components/ShadcnUI/label";
import { Badge } from "@/components/ShadcnUI/badge";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ShadcnUI/card";
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ShadcnUI/tabs";
import { Textarea } from "@/components/ShadcnUI/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ShadcnUI/select";
import { UserPlus, X, Clock, Building, ArrowLeft, Save, Upload, Image as ImageIcon } from "lucide-react";
import { generateTimeSlotsWithDefaults, generateTimeSlotsForMultipleClinics } from "@/lib/utils/time-utils";

interface Clinic {
  id: number;
  clinic_name: string;
  address?: string;
  clinic_start_time?: string;
  clinic_end_time?: string;
  city?: {
    id: number;
    city_name: string;
    state_name?: string;
  };
}

interface AvailabilityTemplate {
  day_of_week: number;
  start_time: string;
  end_time: string;
  duration: number;
  is_available: boolean;
  fee?: number;
  currency?: string;
  clinic_id?: number;
}

const DAYS_OF_WEEK = [
  { value: 6, label: 'SAT' },
  { value: 0, label: 'SUN' },
  { value: 1, label: 'MON' },
  { value: 2, label: 'TUE' },
  { value: 3, label: 'WED' },
  { value: 4, label: 'THU' },
  { value: 5, label: 'FRI' }
];

const generateTimeSlots = (clinicStartTime?: string, clinicEndTime?: string) => {
  return generateTimeSlotsWithDefaults(clinicStartTime, clinicEndTime, 15);
};

export default function AddDoctorPage() {
  const router = useRouter();
  const { setTitle, setSubtitle } = usePageHeader();
  const [loading, setLoading] = useState(false);
  const [clinics, setClinics] = useState<Clinic[]>([]);
  const [clinicsLoading, setClinicsLoading] = useState(false);
  const [imageUploading, setImageUploading] = useState(false);
  
  // Form data
  const [formData, setFormData] = useState({
    display_name: "",
    email: "",
    phone: "",
    specialization_name: "",
    years_of_experience: "",
    biography: "",
    consultation_fees: "",
    consultation_currency: "INR",
    consultation_mode: "both" as "in_person" | "video" | "both",
    profile_image: "",
    languages: [] as string[],
  });
  
  const [selectedClinics, setSelectedClinics] = useState<number[]>([]);
  const [clinicTemplates, setClinicTemplates] = useState<Record<number, AvailabilityTemplate[]>>({});
  
  // Generate time slots based on selected clinic hours
  const timeSlots = useMemo(() => {
    console.log('Generating time slots for clinics:', selectedClinics);
    console.log('Available clinics:', clinics);
    
    if (selectedClinics.length === 0) {
      console.log('No clinics selected, using defaults');
      return generateTimeSlots(); // Use defaults
    }
    
    // Get the selected clinic objects with their hours, filtering out those without defined hours
    const selectedClinicObjects = clinics
      .filter(c => selectedClinics.includes(c.id))
      .filter(c => c.clinic_start_time && c.clinic_end_time)
      .map(c => ({
        clinic_start_time: c.clinic_start_time!,
        clinic_end_time: c.clinic_end_time!
      }));
    
    console.log('Selected clinic objects with hours:', selectedClinicObjects);
    
    // Use the new function that handles multiple clinics
    const result = generateTimeSlotsForMultipleClinics(selectedClinicObjects, 15);
    console.log('Generated time slots:', result);
    return result;
  }, [selectedClinics, clinics]);

  useEffect(() => {
    setTitle("Add New Doctor");
    setSubtitle("Create a new doctor profile and assign clinics");
    fetchClinics();
  }, [setTitle, setSubtitle]);

  const fetchClinics = async () => {
    try {
      setClinicsLoading(true);
      const response = await fetch('/api/v1/admin/clinics?per_page=100');
      const data = await response.json();
      if (data.success) {
        setClinics(data.data.clinics);
      }
    } catch (error) {
      console.error('Error fetching clinics:', error);
    } finally {
      setClinicsLoading(false);
    }
  };

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  const handleLanguageChange = (languages: string[]) => {
    setFormData(prev => ({ ...prev, languages }));
  };

  const handleLanguageInputChange = (value: string) => {
    // Store the raw input value without processing
    setFormData(prev => ({ ...prev, languages: [value] }));
  };

  const handleLanguageInputBlur = () => {
    // Process languages only when user finishes typing
    const rawValue = formData.languages[0] || '';
    const processedLanguages = rawValue
      .split(',')
      .map(lang => lang.trim())
      .filter(lang => lang.length > 0);
    
    setFormData(prev => ({ ...prev, languages: processedLanguages }));
  };

  const handleLanguageInputKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    // Process languages when user presses Enter or comma
    if (e.key === 'Enter' || e.key === ',') {
      e.preventDefault();
      handleLanguageInputBlur();
    }
  };

  const handleImageUpload = async (file: File) => {
    if (!file) return;

    // Validate file type
    const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp'];
    if (!allowedTypes.includes(file.type)) {
      alert('Please select a valid image file (JPEG, PNG, or WebP)');
      return;
    }

    // Validate file size (5MB)
    if (file.size > 5 * 1024 * 1024) {
      alert('Image size must be less than 5MB');
      return;
    }

    try {
      setImageUploading(true);
      
      const formData = new FormData();
      formData.append('file', file);

      const response = await fetch('/api/v1/admin/upload-image', {
        method: 'POST',
        body: formData,
      });

      const data = await response.json();
      
      if (data.success) {
        setFormData(prev => ({ ...prev, profile_image: data.url }));
      } else {
        alert(data.error || 'Failed to upload image');
      }
    } catch (error) {
      console.error('Error uploading image:', error);
      alert('Failed to upload image');
    } finally {
      setImageUploading(false);
    }
  };

  const handleFileInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      handleImageUpload(file);
    }
  };

  const removeProfileImage = () => {
    setFormData(prev => ({ ...prev, profile_image: "" }));
  };

  const handleClinicToggle = (clinicId: number) => {
    setSelectedClinics(prev => {
      // If clinic is already selected, remove it
      if (prev.includes(clinicId)) {
        const newSelected = prev.filter(id => id !== clinicId);
        
        // Remove templates for deselected clinics
        setClinicTemplates(prevTemplates => {
          const newTemplates = { ...prevTemplates };
          delete newTemplates[clinicId];
          return newTemplates;
        });
        
        return newSelected;
      }
      
      // If clinic is not selected, check if we can add it (max 4)
      if (prev.length >= 4) {
        alert('Maximum 4 clinics can be selected');
        return prev;
      }
      
      // Add the clinic
      const newSelected = [...prev, clinicId];
      
      // Initialize templates for newly selected clinics
      setClinicTemplates(prevTemplates => ({
        ...prevTemplates,
        [clinicId]: []
      }));
      
      return newSelected;
    });
  };

  const getTemplateForDayAndTime = (dayOfWeek: number, time: string, clinicId: number) => {
    return clinicTemplates[clinicId]?.find(template => 
      template.day_of_week === dayOfWeek && 
      template.start_time === time
    );
  };

  const isTimeSlotTaken = (dayOfWeek: number, time: string, clinicIdToExclude: number) => {
    for (const clinicId in clinicTemplates) {
      if (Number(clinicId) !== clinicIdToExclude) {
        if (clinicTemplates[Number(clinicId)]?.some(t => t.day_of_week === dayOfWeek && t.start_time === time)) {
          return true;
        }
      }
    }
    return false;
  };

  const toggleTemplateAvailability = (dayOfWeek: number, time: string, clinicId: number) => {
    setClinicTemplates(prev => {
      const currentTemplates = prev[clinicId] || [];
      const existingTemplate = currentTemplates.find(
        t => t.day_of_week === dayOfWeek && t.start_time === time
      );

      if (existingTemplate) {
        // Remove template (deselect)
        return {
          ...prev,
          [clinicId]: currentTemplates.filter(
            t => !(t.day_of_week === dayOfWeek && t.start_time === time)
          )
        };
      } else {
        // Add template (select)
        const [hours, minutes] = time.split(':').map(Number);
        const endTime = new Date();
        endTime.setHours(hours, minutes + 15, 0, 0);
        const endTimeStr = `${endTime.getHours().toString().padStart(2, '0')}:${endTime.getMinutes().toString().padStart(2, '0')}`;
        
        const newTemplate: AvailabilityTemplate = {
          day_of_week: dayOfWeek,
          start_time: time,
          end_time: endTimeStr,
          duration: 15,
          is_available: true,
          fee: formData.consultation_fees ? parseFloat(formData.consultation_fees) : 500,
          currency: formData.consultation_currency,
          clinic_id: clinicId
        };

        return {
          ...prev,
          [clinicId]: [...currentTemplates, newTemplate]
        };
      }
    });
  };

  const handleSubmit = async () => {
    if (!formData.display_name || !formData.email) {
      alert('Please fill in required fields');
      return;
    }

    try {
      setLoading(true);
      
      // Flatten all clinic templates into a single array
      const allTemplates = Object.values(clinicTemplates).flat();
      
      const response = await fetch('/api/v1/admin/doctors', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          ...formData,
          clinic_ids: selectedClinics,
          availability_templates: allTemplates,
        }),
      });

      const data = await response.json();
      if (data.success) {
        router.push('/admin/doctors');
      } else {
        alert(data.error || 'Failed to create doctor');
      }
    } catch (error) {
      console.error('Error creating doctor:', error);
      alert('Failed to create doctor');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <Button
            variant="outline"
            size="sm"
            onClick={() => router.back()}
            className="flex items-center gap-2"
          >
            <ArrowLeft className="h-4 w-4" />
            Back
          </Button>
          <div>
            <h1 className="text-2xl font-bold">Add New Doctor</h1>
            <p className="text-muted-foreground">Create a new doctor profile and assign clinics</p>
          </div>
        </div>
        <Button onClick={handleSubmit} disabled={loading} className="flex items-center gap-2">
          <Save className="h-4 w-4" />
          {loading ? "Creating..." : "Create Doctor"}
        </Button>
      </div>

      {/* Form Tabs */}
      <Tabs defaultValue="basic" className="w-full">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="basic">Basic Information</TabsTrigger>
          <TabsTrigger value="clinics">Clinic Assignment</TabsTrigger>
          <TabsTrigger value="availability">Availability Setup</TabsTrigger>
        </TabsList>
        
        <TabsContent value="basic" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <UserPlus className="h-5 w-5" />
                Personal Information
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-2">
                  <Label htmlFor="display_name">Display Name *</Label>
                  <Input
                    id="display_name"
                    value={formData.display_name}
                    onChange={(e) => handleInputChange('display_name', e.target.value)}
                    placeholder="Dr. John Doe"
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="email">Email *</Label>
                  <Input
                    id="email"
                    type="email"
                    value={formData.email}
                    onChange={(e) => handleInputChange('email', e.target.value)}
                    placeholder="<EMAIL>"
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="phone">Phone</Label>
                  <Input
                    id="phone"
                    value={formData.phone}
                    onChange={(e) => handleInputChange('phone', e.target.value)}
                    placeholder="+1234567890"
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="specialization">Specialization</Label>
                  <Input
                    id="specialization"
                    value={formData.specialization_name}
                    onChange={(e) => handleInputChange('specialization_name', e.target.value)}
                    placeholder="Cardiology, Neurology, etc."
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="years_of_experience">Years of Experience</Label>
                  <Input
                    id="years_of_experience"
                    type="number"
                    value={formData.years_of_experience}
                    onChange={(e) => handleInputChange('years_of_experience', e.target.value)}
                    placeholder="5"
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="consultation_fees">Consultation Fees</Label>
                  <Input
                    id="consultation_fees"
                    type="number"
                    value={formData.consultation_fees}
                    onChange={(e) => handleInputChange('consultation_fees', e.target.value)}
                    placeholder="500"
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="consultation_currency">Currency</Label>
                  <Select value={formData.consultation_currency} onValueChange={(value) => handleInputChange('consultation_currency', value)}>
                    <SelectTrigger>
                      <SelectValue placeholder="Select currency" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="INR">INR</SelectItem>
                      <SelectItem value="USD">USD</SelectItem>
                      <SelectItem value="EUR">EUR</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="consultation_mode">Consultation Mode</Label>
                  <Select value={formData.consultation_mode} onValueChange={(value) => handleInputChange('consultation_mode', value)}>
                    <SelectTrigger>
                      <SelectValue placeholder="Select mode" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="in_person">In Person</SelectItem>
                      <SelectItem value="video">Video</SelectItem>
                      <SelectItem value="both">Both</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="profile_image">Profile Image</Label>
                  <div className="space-y-3">
                    {formData.profile_image ? (
                      <div className="relative inline-block">
                        <img
                          src={formData.profile_image}
                          alt="Profile preview"
                          className="w-24 h-24 rounded-lg object-cover border"
                        />
                        <Button
                          type="button"
                          variant="destructive"
                          size="sm"
                          className="absolute -top-2 -right-2 h-6 w-6 p-0 rounded-full"
                          onClick={removeProfileImage}
                        >
                          <X className="h-3 w-3" />
                        </Button>
                      </div>
                    ) : (
                      <div className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center hover:border-gray-400 transition-colors">
                        <ImageIcon className="h-8 w-8 mx-auto text-gray-400 mb-2" />
                        <p className="text-sm text-gray-600 mb-2">Click to upload profile image</p>
                        <p className="text-xs text-gray-500">JPEG, PNG, WebP up to 5MB</p>
                      </div>
                    )}
                    
                    <div className="flex gap-2">
                      <Button
                        type="button"
                        variant="outline"
                        size="sm"
                        onClick={() => document.getElementById('profile-image-input')?.click()}
                        disabled={imageUploading}
                        className="flex items-center gap-2"
                      >
                        <Upload className="h-4 w-4" />
                        {imageUploading ? 'Uploading...' : 'Choose Image'}
                      </Button>
                      {formData.profile_image && (
                        <Button
                          type="button"
                          variant="outline"
                          size="sm"
                          onClick={removeProfileImage}
                        >
                          Remove
                        </Button>
                      )}
                    </div>
                    
                    <input
                      id="profile-image-input"
                      type="file"
                      accept="image/jpeg,image/jpg,image/png,image/webp"
                      onChange={handleFileInputChange}
                      className="hidden"
                    />
                  </div>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="languages">Languages</Label>
                  <Input
                    id="languages"
                    value={formData.languages.join(', ')}
                    onChange={(e) => handleLanguageInputChange(e.target.value)}
                    onBlur={handleLanguageInputBlur}
                    onKeyDown={handleLanguageInputKeyDown}
                    placeholder="English, Hindi, Spanish"
                  />
                  <p className="text-xs text-muted-foreground">Separate languages with commas</p>
                </div>
              </div>
              <div className="space-y-2">
                <Label htmlFor="biography">Biography</Label>
                <Textarea
                  id="biography"
                  value={formData.biography}
                  onChange={(e) => handleInputChange('biography', e.target.value)}
                  placeholder="Tell us about the doctor's background, education, and expertise..."
                  rows={6}
                />
              </div>
            </CardContent>
          </Card>
        </TabsContent>
        
        <TabsContent value="clinics" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Building className="h-5 w-5" />
                Clinic Assignment
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-6">
              <div>
                <Label>Select Clinics</Label>
                <p className="text-sm text-muted-foreground mb-4">
                  Choose which clinics this doctor will be associated with (Maximum 4 clinics)
                </p>
                <div className="mb-4">
                  <Badge variant={selectedClinics.length >= 4 ? "destructive" : "secondary"}>
                    {selectedClinics.length}/4 clinics selected
                  </Badge>
                </div>
                
                {clinicsLoading ? (
                  <div className="text-center py-8">Loading clinics...</div>
                ) : (
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 max-h-80 overflow-y-auto">
                    {clinics.map((clinic) => {
                      const isSelected = selectedClinics.includes(clinic.id);
                      const isDisabled = !isSelected && selectedClinics.length >= 4;
                      
                      return (
                        <Card 
                          key={clinic.id} 
                          className={`transition-colors ${
                            isSelected 
                              ? 'border-primary bg-primary/5 cursor-pointer' 
                              : isDisabled
                              ? 'border-gray-200 bg-gray-50 cursor-not-allowed opacity-50'
                              : 'hover:bg-muted/50 cursor-pointer'
                          }`}
                          onClick={() => !isDisabled && handleClinicToggle(clinic.id)}
                        >
                        <CardContent className="p-4">
                          <div className="flex items-start justify-between">
                            <div className="flex-1">
                              <div className="font-medium text-sm">{clinic.clinic_name}</div>
                              {clinic.address && (
                                <div className="text-xs text-muted-foreground mt-1">
                                  {clinic.address}
                                </div>
                              )}
                              {clinic.city && (
                                <div className="text-xs text-muted-foreground">
                                  {clinic.city.city_name}
                                  {clinic.city.state_name && `, ${clinic.city.state_name}`}
                                </div>
                              )}
                            </div>
                            {selectedClinics.includes(clinic.id) && (
                              <Badge variant="default" className="ml-2">
                                Selected
                              </Badge>
                            )}
                          </div>
                        </CardContent>
                      </Card>
                    );
                  })}
                  </div>
                )}
                
                {selectedClinics.length > 0 && (
                  <div className="mt-6">
                    <Label>Selected Clinics ({selectedClinics.length})</Label>
                    <div className="flex flex-wrap gap-2 mt-2">
                      {selectedClinics.map((clinicId) => {
                        const clinic = clinics.find(c => c.id === clinicId);
                        return clinic ? (
                          <Badge key={clinicId} variant="secondary" className="flex items-center gap-1">
                            <Building className="h-3 w-3" />
                            {clinic.clinic_name}
                            <X 
                              className="h-3 w-3 cursor-pointer" 
                              onClick={(e) => {
                                e.stopPropagation();
                                handleClinicToggle(clinicId);
                              }}
                            />
                          </Badge>
                        ) : null;
                      })}
                    </div>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        </TabsContent>
        
        <TabsContent value="availability" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Clock className="h-5 w-5" />
                Availability Setup
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-6">
              <div>
                <Label>Availability Templates</Label>
                <p className="text-sm text-muted-foreground mb-4">
                  Set weekly availability patterns for each clinic. Click time slots to toggle availability.
                </p>
                
                {selectedClinics.length === 0 ? (
                  <div className="text-center py-12 text-muted-foreground">
                    <Clock className="h-12 w-12 mx-auto mb-4" />
                    <p className="text-lg font-medium">No clinics selected</p>
                    <p className="text-sm">Please select clinics first to configure availability</p>
                  </div>
                ) : (
                  <Tabs defaultValue={selectedClinics[0]?.toString()} className="w-full">
                    <TabsList className="grid w-full" style={{ gridTemplateColumns: `repeat(${selectedClinics.length}, 1fr)` }}>
                      {selectedClinics.map((clinicId) => {
                        const clinic = clinics.find(c => c.id === clinicId);
                        return (
                          <TabsTrigger key={clinicId} value={clinicId.toString()}>
                            {clinic?.clinic_name || `Clinic ${clinicId}`}
                          </TabsTrigger>
                        );
                      })}
                    </TabsList>
                    
                    {selectedClinics.map((clinicId) => {
                      const clinic = clinics.find(c => c.id === clinicId);
                      const clinicTemplateCount = clinicTemplates[clinicId]?.length || 0;
                      
                      return (
                        <TabsContent key={clinicId} value={clinicId.toString()} className="space-y-4">
                          <div className="flex items-center justify-between">
                            <div>
                              <h4 className="font-medium">{clinic?.clinic_name}</h4>
                              {clinic?.address && (
                                <p className="text-sm text-muted-foreground">{clinic.address}</p>
                              )}
                            </div>
                            <Badge variant="secondary">
                              {clinicTemplateCount} slots
                            </Badge>
                          </div>
                          
                          <div className="border rounded-lg overflow-hidden">
                            <div className="grid grid-cols-8 border-b bg-muted/50">
                              <div className="p-3 text-sm font-medium">Time</div>
                              {DAYS_OF_WEEK.map((day) => (
                                <div key={day.value} className="p-3 text-center text-sm font-medium border-l">
                                  {day.label}
                                </div>
                              ))}
                            </div>
                            
                            <div className="max-h-80 overflow-y-auto">
                              {timeSlots.map((time) => (
                                <div key={time} className="grid grid-cols-8 border-b">
                                  <div className="p-3 text-xs text-muted-foreground border-r flex items-center justify-center">
                                    {time}
                                  </div>
                                  {DAYS_OF_WEEK.map((day) => {
                                    const template = getTemplateForDayAndTime(day.value, time, clinicId);
                                    const isAvailable = template && template.is_available;
                                    const isOccupiedElsewhere = isTimeSlotTaken(day.value, time, clinicId);

                                    return (
                                      <div
                                        key={`${day.value}-${time}`}
                                        className="p-2 border-l min-h-[50px] flex items-center justify-center"
                                      >
                                        <div
                                          className={`w-full h-full rounded transition-colors flex items-center justify-center text-sm ${
                                            isAvailable
                                              ? "bg-blue-100 hover:bg-blue-200 text-blue-800 cursor-pointer"
                                              : isOccupiedElsewhere
                                              ? "bg-gray-200 text-gray-400 cursor-not-allowed pointer-events-none"
                                              : "bg-gray-100 hover:bg-gray-200 text-gray-600 cursor-pointer"
                                          }`}
                                          onClick={() => toggleTemplateAvailability(day.value, time, clinicId)}
                                        >
                                          {isAvailable ? "✓" : ""}
                                        </div>
                                      </div>
                                    );
                                  })}
                                </div>
                              ))}
                            </div>
                          </div>
                        </TabsContent>
                      );
                    })}
                  </Tabs>
                )}
                
                {Object.values(clinicTemplates).flat().length > 0 && (
                  <div className="mt-6">
                    <Label>Total Time Slots ({Object.values(clinicTemplates).flat().length})</Label>
                    <div className="text-sm text-muted-foreground">
                      <Clock className="h-4 w-4 inline mr-1" />
                      {Object.values(clinicTemplates).flat().length} time slots configured across all clinics
                    </div>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
