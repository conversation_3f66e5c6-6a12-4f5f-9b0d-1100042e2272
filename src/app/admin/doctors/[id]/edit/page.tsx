"use client";
import React, { useState, useEffect, useMemo } from "react";
import { useRouter, usePara<PERSON> } from "next/navigation";
import { usePageHeader } from "@/contexts/PageHeaderContext";
import { <PERSON><PERSON> } from "@/components/ShadcnUI/button";
import { Input } from "@/components/ShadcnUI/input";
import { Label } from "@/components/ShadcnUI/label";
import { Badge } from "@/components/ShadcnUI/badge";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ShadcnUI/card";
import { Ta<PERSON>, Ta<PERSON>Content, TabsList, TabsTrigger } from "@/components/ShadcnUI/tabs";
import { Textarea } from "@/components/ShadcnUI/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ShadcnUI/select";
import { X, Building, Clock, ArrowLeft, Save, Loader2, Upload } from "lucide-react";
import { FileUploadService } from "@/lib/services/file-upload.service";
import { generateTimeSlotsWithDefaults, generateTimeSlotsForMultipleClinics } from "@/lib/utils/time-utils";

interface Clinic {
  id: number;
  clinic_name: string;
  address?: string;
  city?: {
    id: number;
    city_name: string;
    state_name?: string;
  };
  clinic_start_time?: string;
  clinic_end_time?: string;
}

interface AvailabilityTemplate {
  id: number;
  day_of_week: number;
  start_time: string;
  end_time: string;
  duration: number;
  is_available: boolean;
  fee?: number;
  currency?: string;
  clinic_id?: number;
}

interface Doctor {
  id: number;
  specialization_name?: string;
  years_of_experience?: number;
  consultation_fees?: number;
  consultation_currency?: string;
  consultation_mode?: "in_person" | "video" | "both";
  profile: {
    id: number;
    display_name: string;
    email: string;
    phone?: string;
    auth_id: string;
    biography?: string;
    profile_image?: string;
    languages?: string[];
  };
  doctor_clinics?: {
    clinic: {
      id: number;
      clinic_name: string;
      address?: string;
      city?: {
        id: number;
        city_name: string;
        state_name?: string;
      };
    };
  }[];
  _count?: {
    appointments: number;
    availability_templates: number;
  };
}

const DAYS_OF_WEEK = [
  { value: 6, label: 'SAT' },
  { value: 0, label: 'SUN' },
  { value: 1, label: 'MON' },
  { value: 2, label: 'TUE' },
  { value: 3, label: 'WED' },
  { value: 4, label: 'THU' },
  { value: 5, label: 'FRI' }
];

export default function EditDoctorPage() {
  const router = useRouter();
  const params = useParams();
  const { setTitle, setSubtitle } = usePageHeader();
  const [loading, setLoading] = useState(false);
  const [doctor, setDoctor] = useState<Doctor | null>(null);
  const [clinics, setClinics] = useState<Clinic[]>([]);
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [uploadingImage, setUploadingImage] = useState(false);
  const [imagePreview, setImagePreview] = useState<string | null>(null);
  const [clinicsLoading, setClinicsLoading] = useState(false);

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  const [availabilityTemplates, setAvailabilityTemplates] = useState<AvailabilityTemplate[]>([]);
  const [clinicTemplates, setClinicTemplates] = useState<Record<number, Record<string, boolean>>>({});
  
  // Form data
  const [formData, setFormData] = useState({
    display_name: "",
    email: "",
    phone: "",
    specialization_name: "",
    years_of_experience: "",
    biography: "",
    consultation_fees: "",
    consultation_currency: "INR",
    consultation_mode: "both" as "in_person" | "video" | "both",
    profile_image: "",
    languages: [] as string[],
  });
  
  const [selectedClinics, setSelectedClinics] = useState<number[]>([]);
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  const [originalClinics, setOriginalClinics] = useState<number[]>([]);
  
  // Generate time slots based on selected clinic hours
  const timeSlots = useMemo(() => {
    if (selectedClinics.length === 0) {
      return generateTimeSlotsWithDefaults(); // Use defaults
    }
    
    // Get the selected clinic objects with their hours, filtering out those without defined hours
    const selectedClinicObjects = clinics
      .filter(c => selectedClinics.includes(c.id))
      .filter(c => c.clinic_start_time && c.clinic_end_time)
      .map(c => ({
        clinic_start_time: c.clinic_start_time!,
        clinic_end_time: c.clinic_end_time!
      }));
    
    // Use the new function that handles multiple clinics
    return generateTimeSlotsForMultipleClinics(selectedClinicObjects, 15);
  }, [selectedClinics, clinics]);

  useEffect(() => {
    if (params.id) {
      fetchDoctor();
      fetchClinics();
    }
  }, [params.id]);

  useEffect(() => {
    if (doctor) {
      setTitle(`Edit Doctor - ${doctor.profile.display_name}`);
      setSubtitle("Update doctor profile and clinic assignments");
    }
  }, [doctor, setTitle, setSubtitle]);

  // Cleanup image preview URL when component unmounts or image changes
  useEffect(() => {
    return () => {
      if (imagePreview) {
        URL.revokeObjectURL(imagePreview);
      }
    };
  }, [imagePreview]);

  const fetchDoctor = async () => {
    try {
      const response = await fetch(`/api/v1/admin/doctors/edit/${params.id}`);
      const data = await response.json();
      if (data.success) {
        setDoctor(data.data);
        
        // Initialize form data
        setFormData({
          display_name: data.data.profile.display_name || "",
          email: data.data.profile.email || "",
          phone: data.data.profile.phone || "",
          specialization_name: data.data.specialization_name || "",
          years_of_experience: data.data.years_of_experience?.toString() || "",
          biography: data.data.profile.biography || "",
          consultation_fees: data.data.consultation_fees?.toString() || "",
          consultation_currency: data.data.consultation_currency || "INR",
          consultation_mode: data.data.consultation_mode || "both",
          profile_image: data.data.profile.profile_image || "",
          languages: data.data.profile.languages || [],
        });
        
        // Initialize selected clinics and original clinics
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        const clinicIds = data.data.doctor_clinics?.map((dc: any) => dc.clinic.id) || [];
        setSelectedClinics(clinicIds);
        setOriginalClinics(clinicIds);
        
        fetchAvailabilityTemplates(data.data.id);
      }
    } catch (error) {
      console.error('Error fetching doctor:', error);
    }
  };

  const fetchClinics = async () => {
    try {
      setClinicsLoading(true);
      const response = await fetch('/api/v1/admin/clinics?per_page=100');
      const data = await response.json();
      if (data.success) {
        setClinics(data.data.clinics);
      }
    } catch (error) {
      console.error('Error fetching clinics:', error);
    } finally {
      setClinicsLoading(false);
    }
  };

  const fetchAvailabilityTemplates = async (doctorId: number) => {
    try {
      const response = await fetch(`/api/v1/admin/availability-templates?doctor_id=${doctorId}`);
      const data = await response.json();
      if (data.success) {
        setAvailabilityTemplates(data.data);
        
        // Organize templates by clinic and convert to editable format
        const templatesByClinic: Record<number, Record<string, boolean>> = {};
        data.data.forEach((template: AvailabilityTemplate) => {
          const clinicId = template.clinic_id || 0; // Use 0 for templates without clinic
          if (!templatesByClinic[clinicId]) {
            templatesByClinic[clinicId] = {};
          }
          const key = `${template.day_of_week}-${template.start_time}`;
          templatesByClinic[clinicId][key] = template.is_available;
        });
        setClinicTemplates(templatesByClinic);
      }
    } catch (error) {
      console.error('Error fetching availability templates:', error);
    }
  };

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  const handleLanguageChange = (languages: string[]) => {
    setFormData(prev => ({ ...prev, languages }));
  };

  const handleClinicToggle = (clinicId: number) => {
    setSelectedClinics(prev => {
      // If clinic is already selected, remove it
      if (prev.includes(clinicId)) {
        // Clear availability templates for the deselected clinic
        setClinicTemplates(current => {
          const updated = { ...current };
          delete updated[clinicId];
          return updated;
        });
        return prev.filter(id => id !== clinicId);
      }
      
      // If clinic is not selected, check if we can add it (max 4)
      if (prev.length >= 4) {
        alert('Maximum 4 clinics can be selected');
        return prev;
      }
      
      // Add the clinic
      return [...prev, clinicId];
    });
  };

  const getTemplateAvailability = (dayOfWeek: number, time: string, clinicId: number) => {
    const key = `${dayOfWeek}-${time}`;
    return clinicTemplates[clinicId]?.[key] || false;
  };

  const isTimeSlotTaken = (dayOfWeek: number, time: string, clinicIdToExclude: number) => {
    for (const clinicId in clinicTemplates) {
      if (Number(clinicId) !== clinicIdToExclude) {
        const key = `${dayOfWeek}-${time}`;
        if (clinicTemplates[Number(clinicId)]?.[key]) {
          return true;
        }
      }
    }
    return false;
  };

  const toggleTemplateAvailability = (dayOfWeek: number, time: string, clinicId: number) => {
    const key = `${dayOfWeek}-${time}`;
    setClinicTemplates(prev => ({
      ...prev,
      [clinicId]: {
        ...(prev[clinicId] || {}),
        [key]: !prev[clinicId]?.[key]
      }
    }));
  };

  const createAvailabilityTemplates = async () => {
    if (!doctor) return;

    // First, delete all existing templates for this doctor
    try {
      await fetch(`/api/v1/admin/availability-templates?doctor_id=${doctor.id}`, {
        method: 'DELETE',
      });
    } catch (error) {
      console.error('Error deleting existing templates:', error);
    }

    // Then create new templates for selected clinics
    const templatesToCreate = [];
    
    for (const clinicId of selectedClinics) {
      const clinicTemplateData = clinicTemplates[clinicId] || {};
      
      for (const timeSlot of timeSlots) {
        for (const day of DAYS_OF_WEEK) {
          const key = `${day.value}-${timeSlot}`;
          if (clinicTemplateData[key]) {
            // Calculate end time (15 minutes later)
            const [hours, minutes] = timeSlot.split(':').map(Number);
            const endMinutes = minutes + 15;
            const endHours = hours + Math.floor(endMinutes / 60);
            const endTime = `${endHours.toString().padStart(2, '0')}:${(endMinutes % 60).toString().padStart(2, '0')}`;
            
            templatesToCreate.push({
              clinicId: clinicId,
              dayOfWeek: day.value,
              startTime: timeSlot,
              endTime: endTime,
              duration: 15,
              isAvailable: true,
              fee: formData.consultation_fees ? parseFloat(formData.consultation_fees) : null,
              currency: formData.consultation_currency
            });
          }
        }
      }
    }
    
    if (templatesToCreate.length > 0) {
      try {
        const response = await fetch('/api/v1/admin/availability-templates', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            doctorId: doctor.id,
            templates: templatesToCreate
          }),
        });
        
        if (!response.ok) {
          throw new Error('Failed to create availability templates');
        }
      } catch (error) {
        console.error('Error creating availability templates:', error);
        throw error;
      }
    }
  };

  const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      setSelectedFile(file);
      
      // Create preview URL
      const previewUrl = URL.createObjectURL(file);
      setImagePreview(previewUrl);
    }
  };

  const handleRemoveImage = () => {
    setSelectedFile(null);
    setImagePreview(null);
    // Clear the profile_image field
    setFormData(prev => ({ ...prev, profile_image: '' }));
  };

  const uploadProfileImage = async (): Promise<string | null> => {
    if (!selectedFile) {
      return formData.profile_image || null;
    }

    try {
      setUploadingImage(true);
      const result = await FileUploadService.uploadImage(selectedFile);
      
      if (result.success && result.url) {
        return result.url;
      } else {
        throw new Error(result.error || 'Failed to upload image');
      }
    } catch (error) {
      console.error('Error uploading image:', error);
      alert('Failed to upload image. Please try again.');
      return null;
    } finally {
      setUploadingImage(false);
    }
  };

  const handleSubmit = async () => {
    if (!doctor || !formData.display_name || !formData.email) {
      alert('Please fill in required fields');
      return;
    }

    try {
      setLoading(true);
      
      // Upload image first if a new file is selected
      let profileImageUrl = formData.profile_image;
      if (selectedFile) {
        const uploadedUrl = await uploadProfileImage();
        if (uploadedUrl) {
          profileImageUrl = uploadedUrl;
        } else {
          // If image upload failed, don't proceed
          return;
        }
      }
      
      // Update doctor (includes both profile and doctor fields)
      const doctorResponse = await fetch(`/api/v1/admin/doctors/${doctor.profile.auth_id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          display_name: formData.display_name,
          email: formData.email,
          phone: formData.phone,
          biography: formData.biography,
          profile_image: profileImageUrl,
          languages: formData.languages,
          specialization_name: formData.specialization_name,
          years_of_experience: formData.years_of_experience,
          consultation_fees: formData.consultation_fees,
          consultation_currency: formData.consultation_currency,
          consultation_mode: formData.consultation_mode,
          clinic_ids: selectedClinics,
        }),
      });

      if (doctorResponse.ok) {
        // Create availability templates for newly assigned clinics
        await createAvailabilityTemplates();
        
        router.push('/admin/doctors');
      } else {
        alert('Failed to update doctor');
      }
    } catch (error) {
      console.error('Error updating doctor:', error);
      alert('Failed to update doctor');
    } finally {
      setLoading(false);
    }
  };

  if (!doctor) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <Loader2 className="h-8 w-8 animate-spin" />
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <Button
            variant="outline"
            size="sm"
            onClick={() => router.back()}
            className="flex items-center gap-2"
          >
            <ArrowLeft className="h-4 w-4" />
            Back
          </Button>
          <div>
            <h1 className="text-2xl font-bold">Edit Doctor - {doctor.profile.display_name}</h1>
            <p className="text-muted-foreground">Update doctor profile and clinic assignments</p>
          </div>
        </div>
        <Button onClick={handleSubmit} disabled={loading} className="flex items-center gap-2">
          <Save className="h-4 w-4" />
          {loading ? "Updating..." : "Update Doctor"}
        </Button>
      </div>

      {/* Form Tabs */}
      <Tabs defaultValue="basic" className="w-full">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="basic">Basic Information</TabsTrigger>
          <TabsTrigger value="clinics">Clinic Assignment</TabsTrigger>
          <TabsTrigger value="availability">Availability Setup</TabsTrigger>
        </TabsList>
        
        <TabsContent value="basic" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Personal Information</CardTitle>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-2">
                  <Label htmlFor="display_name">Display Name *</Label>
                  <Input
                    id="display_name"
                    value={formData.display_name}
                    onChange={(e) => handleInputChange('display_name', e.target.value)}
                    placeholder="Dr. John Doe"
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="email">Email *</Label>
                  <Input
                    id="email"
                    type="email"
                    value={formData.email}
                    onChange={(e) => handleInputChange('email', e.target.value)}
                    placeholder="<EMAIL>"
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="phone">Phone</Label>
                  <Input
                    id="phone"
                    value={formData.phone}
                    onChange={(e) => handleInputChange('phone', e.target.value)}
                    placeholder="+1234567890"
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="specialization">Specialization</Label>
                  <Input
                    id="specialization"
                    value={formData.specialization_name}
                    onChange={(e) => handleInputChange('specialization_name', e.target.value)}
                    placeholder="Cardiology, Neurology, etc."
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="years_of_experience">Years of Experience</Label>
                  <Input
                    id="years_of_experience"
                    type="number"
                    value={formData.years_of_experience}
                    onChange={(e) => handleInputChange('years_of_experience', e.target.value)}
                    placeholder="5"
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="consultation_fees">Consultation Fees</Label>
                  <Input
                    id="consultation_fees"
                    type="number"
                    value={formData.consultation_fees}
                    onChange={(e) => handleInputChange('consultation_fees', e.target.value)}
                    placeholder="500"
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="consultation_currency">Currency</Label>
                  <Select value={formData.consultation_currency} onValueChange={(value) => handleInputChange('consultation_currency', value)}>
                    <SelectTrigger>
                      <SelectValue placeholder="Select currency" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="INR">INR</SelectItem>
                      <SelectItem value="USD">USD</SelectItem>
                      <SelectItem value="EUR">EUR</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="consultation_mode">Consultation Mode</Label>
                  <Select value={formData.consultation_mode} onValueChange={(value) => handleInputChange('consultation_mode', value)}>
                    <SelectTrigger>
                      <SelectValue placeholder="Select mode" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="in_person">In Person</SelectItem>
                      <SelectItem value="video">Video</SelectItem>
                      <SelectItem value="both">Both</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="profile_image">Profile Image</Label>
                  <div className="space-y-4">
                    {/* Current image preview */}
                    {formData.profile_image && !imagePreview && (
                      <div className="relative inline-block">
                        <img
                          src={formData.profile_image}
                          alt="Current profile"
                          className="w-24 h-24 object-cover rounded-lg border"
                        />
                        <Button
                          type="button"
                          variant="destructive"
                          size="sm"
                          className="absolute -top-2 -right-2 h-6 w-6 p-0 rounded-full"
                          onClick={handleRemoveImage}
                        >
                          <X className="h-3 w-3" />
                        </Button>
                      </div>
                    )}
                    
                    {/* New image preview */}
                    {imagePreview && (
                      <div className="relative inline-block">
                        <img
                          src={imagePreview}
                          alt="New profile preview"
                          className="w-24 h-24 object-cover rounded-lg border"
                        />
                        <Button
                          type="button"
                          variant="destructive"
                          size="sm"
                          className="absolute -top-2 -right-2 h-6 w-6 p-0 rounded-full"
                          onClick={handleRemoveImage}
                        >
                          <X className="h-3 w-3" />
                        </Button>
                      </div>
                    )}
                    
                    {/* File input */}
                    <div className="flex items-center gap-2">
                      <Input
                        id="profile_image"
                        type="file"
                        accept="image/jpeg,image/jpg,image/png,image/webp"
                        onChange={handleFileSelect}
                        className="hidden"
                      />
                      <Button
                        type="button"
                        variant="outline"
                        onClick={() => document.getElementById('profile_image')?.click()}
                        disabled={uploadingImage}
                        className="flex items-center gap-2"
                      >
                        <Upload className="h-4 w-4" />
                        {uploadingImage ? 'Uploading...' : 'Choose Image'}
                      </Button>
                      {selectedFile && (
                        <span className="text-sm text-muted-foreground">
                          {selectedFile.name}
                        </span>
                      )}
                    </div>
                    <p className="text-xs text-muted-foreground">
                      Supported formats: JPEG, PNG, WebP. Max size: 5MB.
                    </p>
                  </div>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="languages">Languages</Label>
                  <Input
                    id="languages"
                    value={formData.languages.join(', ')}
                    onChange={(e) => handleLanguageChange(e.target.value.split(',').map(lang => lang.trim()).filter(lang => lang))}
                    placeholder="English, Hindi, Spanish"
                  />
                  <p className="text-xs text-muted-foreground">Separate languages with commas</p>
                </div>
              </div>
              <div className="space-y-2">
                <Label htmlFor="biography">Biography</Label>
                <Textarea
                  id="biography"
                  value={formData.biography}
                  onChange={(e) => handleInputChange('biography', e.target.value)}
                  placeholder="Tell us about the doctor's background, education, and expertise..."
                  rows={6}
                />
              </div>
            </CardContent>
          </Card>
        </TabsContent>
        
        <TabsContent value="clinics" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Building className="h-5 w-5" />
                Clinic Assignment
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-6">
              <div>
                <Label>Select Clinics</Label>
                <p className="text-sm text-muted-foreground mb-4">
                  Choose which clinics this doctor will be associated with (Maximum 4 clinics)
                </p>
                <div className="mb-4">
                  <Badge variant={selectedClinics.length >= 4 ? "destructive" : "secondary"}>
                    {selectedClinics.length}/4 clinics selected
                  </Badge>
                </div>
                
                {clinicsLoading ? (
                  <div className="text-center py-8">Loading clinics...</div>
                ) : (
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 max-h-80 overflow-y-auto">
                    {clinics.map((clinic) => {
                      const isSelected = selectedClinics.includes(clinic.id);
                      const isDisabled = !isSelected && selectedClinics.length >= 4;
                      
                      return (
                        <Card 
                          key={clinic.id} 
                          className={`transition-colors ${
                            isSelected 
                              ? 'border-primary bg-primary/5 cursor-pointer' 
                              : isDisabled
                              ? 'border-gray-200 bg-gray-50 cursor-not-allowed opacity-50'
                              : 'hover:bg-muted/50 cursor-pointer'
                          }`}
                          onClick={() => !isDisabled && handleClinicToggle(clinic.id)}
                        >
                        <CardContent className="p-4">
                          <div className="flex items-start justify-between">
                            <div className="flex-1">
                              <div className="font-medium text-sm">{clinic.clinic_name}</div>
                              {clinic.address && (
                                <div className="text-xs text-muted-foreground mt-1">
                                  {clinic.address}
                                </div>
                              )}
                              {clinic.city && (
                                <div className="text-xs text-muted-foreground">
                                  {clinic.city.city_name}
                                  {clinic.city.state_name && `, ${clinic.city.state_name}`}
                                </div>
                              )}
                            </div>
                            {selectedClinics.includes(clinic.id) && (
                              <Badge variant="default" className="ml-2">
                                Selected
                              </Badge>
                            )}
                          </div>
                        </CardContent>
                      </Card>
                    );
                  })}
                  </div>
                )}
                
                {selectedClinics.length > 0 && (
                  <div className="mt-6">
                    <Label>Selected Clinics ({selectedClinics.length})</Label>
                    <div className="flex flex-wrap gap-2 mt-2">
                      {selectedClinics.map((clinicId) => {
                        const clinic = clinics.find(c => c.id === clinicId);
                        return clinic ? (
                          <Badge key={clinicId} variant="secondary" className="flex items-center gap-1">
                            <Building className="h-3 w-3" />
                            {clinic.clinic_name}
                            <X 
                              className="h-3 w-3 cursor-pointer" 
                              onClick={(e) => {
                                e.stopPropagation();
                                // Clear availability templates for the deselected clinic
                                setClinicTemplates(current => {
                                  const updated = { ...current };
                                  delete updated[clinicId];
                                  return updated;
                                });
                                setSelectedClinics(prev => prev.filter(id => id !== clinicId));
                              }}
                            />
                          </Badge>
                        ) : null;
                      })}
                    </div>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        </TabsContent>
        
        <TabsContent value="availability" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Clock className="h-5 w-5" />
                Availability Setup
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-6">
              <div>
                <Label>Availability Templates</Label>
                <p className="text-sm text-muted-foreground mb-4">
                  Set up availability for newly assigned clinics. Existing templates can be modified via the Time Management page.
                </p>
                
                {selectedClinics.length === 0 ? (
                  <div className="text-center py-12 text-muted-foreground">
                    <Clock className="h-12 w-12 mx-auto mb-4" />
                    <p className="text-lg font-medium">No clinics selected</p>
                    <p className="text-sm">Select clinics first to set up availability</p>
                  </div>
                ) : (
                  <Tabs defaultValue={selectedClinics[0]?.toString()} className="w-full">
                    <TabsList className="grid w-full" style={{ gridTemplateColumns: `repeat(${selectedClinics.length}, 1fr)` }}>
                      {selectedClinics.map((clinicId) => {
                        const clinic = clinics.find(c => c.id === clinicId);
                        const hasExistingTemplates = clinicTemplates[clinicId] && Object.keys(clinicTemplates[clinicId]).length > 0;
                        return (
                          <TabsTrigger key={clinicId} value={clinicId.toString()}>
                            {clinic?.clinic_name || `Clinic ${clinicId}`}
                            {hasExistingTemplates && <Badge variant="secondary" className="ml-1 text-xs">Existing</Badge>}
                          </TabsTrigger>
                        );
                      })}
                    </TabsList>
                    
                    {selectedClinics.map((clinicId) => {
                      const clinic = clinics.find(c => c.id === clinicId);
                      // eslint-disable-next-line @typescript-eslint/no-unused-vars
                      const hasExistingTemplates = clinicTemplates[clinicId] && Object.keys(clinicTemplates[clinicId]).length > 0;
                      
                      return (
                        <TabsContent key={clinicId} value={clinicId.toString()} className="space-y-4">
                          <div className="flex items-center justify-between">
                            <div>
                              <h4 className="font-medium">{clinic?.clinic_name || `Clinic ${clinicId}`}</h4>
                              {clinic?.address && (
                                <p className="text-sm text-muted-foreground">{clinic.address}</p>
                              )}
                            </div>
                            <Badge variant="default">
                              {Object.keys(clinicTemplates[clinicId] || {}).length} slots configured
                            </Badge>
                          </div>
                          
                          <div className="border rounded-lg overflow-hidden">
                            <div className="grid grid-cols-8 border-b bg-muted/50">
                              <div className="p-3 text-sm font-medium">Time</div>
                              {DAYS_OF_WEEK.map((day) => (
                                <div key={day.value} className="p-3 text-center text-sm font-medium border-l">
                                  {day.label}
                                </div>
                              ))}
                            </div>
                            
                            <div className="max-h-80 overflow-y-auto">
                              {timeSlots.map((time) => (
                                <div key={time} className="grid grid-cols-8 border-b">
                                  <div className="p-3 text-xs text-muted-foreground border-r flex items-center justify-center">
                                    {time}
                                  </div>
                                  {DAYS_OF_WEEK.map((day) => {
                                    const isAvailable = getTemplateAvailability(day.value, time, clinicId);
                                    const isOccupiedElsewhere = isTimeSlotTaken(day.value, time, clinicId);

                                    return (
                                      <div
                                        key={`${day.value}-${time}`}
                                        className="p-2 border-l min-h-[50px] flex items-center justify-center"
                                      >
                                        <div
                                          className={`w-full h-full rounded transition-colors flex items-center justify-center text-sm ${
                                            isAvailable
                                              ? "bg-blue-100 hover:bg-blue-200 text-blue-800 cursor-pointer"
                                              : isOccupiedElsewhere
                                              ? "bg-gray-200 text-gray-400 cursor-not-allowed hover:cursor-not-allowed pointer-events-none"
                                              : "bg-gray-100 hover:bg-gray-200 text-gray-600 cursor-pointer"
                                          }`}
                                          onClick={() => toggleTemplateAvailability(day.value, time, clinicId)}
                                        >
                                          {isAvailable ? "✓" : ""}
                                        </div>
                                      </div>
                                    );
                                  })}
                                </div>
                              ))}
                            </div>
                          </div>
                        </TabsContent>
                      );
                    })}
                  </Tabs>
                )}
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
