import { NextRequest, NextResponse } from "next/server";
import { PrismaClient, AppointmentStatus, ConsultationType } from "@/generated/prisma";
import { serializePrismaResponse } from "@/lib/utils/prisma-serializer";

const prisma = new PrismaClient();

// GET - Fetch appointments with pagination and filtering
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get("page") || "1");
    const perPage = parseInt(searchParams.get("per_page") || "10");
    const status = searchParams.get("status") as AppointmentStatus | null;
    const consultationType = searchParams.get("consultation_type") as ConsultationType | null;
    const doctorId = searchParams.get("doctor_id");
    const clinicId = searchParams.get("clinic_id");
    const startDate = searchParams.get("start_date");
    const endDate = searchParams.get("end_date");
    const search = searchParams.get("search") || "";

    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    const where: any = {};

    if (status) {
      where.status = status;
    }

    if (consultationType) {
      where.consultation_type = consultationType;
    }

    if (doctorId) {
      where.doctor_id = parseInt(doctorId);
    }

    if (clinicId) {
      where.clinic_id = parseInt(clinicId);
    }

    if (startDate && endDate) {
      where.appointment_date = {
        gte: new Date(startDate),
        lte: new Date(endDate)
      };
    }

    if (search) {
      where.OR = [
        {
          patient: {
            profile: {
              display_name: {
                contains: search,
                mode: 'insensitive' as const
              }
            }
          }
        },
        {
          patient: {
            profile: {
              email: {
                contains: search,
                mode: 'insensitive' as const
              }
            }
          }
        },
        {
          id: {
            equals: parseInt(search) || undefined
          }
        }
      ];
    }

    const [appointments, total] = await Promise.all([
      prisma.appointments.findMany({
        where,
        take: perPage,
        skip: (page - 1) * perPage,
        include: {
          doctor: {
            include: {
              profile: {
                select: {
                  display_name: true,
                  email: true
                }
              }
            }
          },
          patient: {
            include: {
              profile: {
                select: {
                  display_name: true,
                  email: true,
                  phone: true
                }
              }
            }
          },
          clinic: {
            select: {
              clinic_name: true,
              address: true
            }
          }
        },
        orderBy: { appointment_date: 'desc' }
      }),
      prisma.appointments.count({ where })
    ]);

    return NextResponse.json({
      success: true,
      data: {
        appointments: serializePrismaResponse(appointments),
        total,
        page,
        perPage,
        totalPages: Math.ceil(total / perPage)
      }
    });
  } catch (error) {
    console.error('Error fetching appointments:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to fetch appointments' },
      { status: 500 }
    );
  }
}

// POST - Create a new appointment
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const {
      doctorId,
      patientId,
      clinicId,
      appointmentDate,
      startTime,
      endTime,
      consultationType,
      duration,
      fees,
      currency
    } = body;

    // Validate required fields
    if (!doctorId || !clinicId || !appointmentDate || !startTime || !endTime || !consultationType || !duration || !fees || !currency) {
      return NextResponse.json(
        { success: false, error: 'Missing required fields' },
        { status: 400 }
      );
    }

    // Check if the time slot is available
    const existingAppointment = await prisma.appointments.findFirst({
      where: {
        doctor_id: parseInt(doctorId),
        appointment_date: new Date(appointmentDate),
        start_time: new Date(startTime),
        end_time: new Date(endTime),
        status: {
          not: 'cancelled'
        }
      }
    });

    if (existingAppointment) {
      return NextResponse.json(
        { success: false, error: 'Time slot is already booked' },
        { status: 409 }
      );
    }

    const appointment = await prisma.appointments.create({
      data: {
        doctor_id: parseInt(doctorId),
        patient_id: patientId ? parseInt(patientId) : null,
        clinic_id: parseInt(clinicId),
        appointment_date: new Date(appointmentDate),
        start_time: new Date(startTime),
        end_time: new Date(endTime),
        consultation_type: consultationType,
        status: 'upcoming',
        duration: parseInt(duration),
        fees: parseFloat(fees),
        currency,
        payment_status: 'unpaid',
        booking_date: new Date()
      },
      include: {
        doctor: {
          include: {
            profile: {
              select: {
                display_name: true,
                email: true
              }
            }
          }
        },
        patient: {
          include: {
            profile: {
              select: {
                display_name: true,
                email: true,
                phone: true
              }
            }
          }
        },
        clinic: {
          select: {
            clinic_name: true,
            address: true
          }
        }
      }
    });

    return NextResponse.json({
      success: true,
      data: serializePrismaResponse(appointment)
    });
  } catch (error) {
    console.error('Error creating appointment:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to create appointment' },
      { status: 500 }
    );
  }
}
