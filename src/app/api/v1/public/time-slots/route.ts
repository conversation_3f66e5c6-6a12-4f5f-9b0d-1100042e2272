import { NextRequest, NextResponse } from "next/server";
import { PrismaClient } from "@/generated/prisma";
import { serializePrismaResponse } from "@/lib/utils/prisma-serializer";
import { TimeSlot } from "@/types/api/public/api-timeslots";

const prisma = new PrismaClient();

// GET - Fetch available time slots for a doctor based on availability templates and unavailabilities
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const doctorId = searchParams.get("doctor_id");
    const clinicId = searchParams.get("clinic_id");
    const startDate = searchParams.get("start_date");
    const endDate = searchParams.get("end_date");

    if (!doctorId) {
      return NextResponse.json(
        { success: false, error: 'Doctor ID is required' },
        { status: 400 }
      );
    }

    // Default to next 7 days if no date range provided
    const start = startDate ? new Date(startDate) : new Date();
    const end = endDate ? new Date(endDate) : new Date(Date.now() + 6 * 24 * 60 * 60 * 1000);

    // Ensure we're looking at future dates only
    const now = new Date();
    if (start < now) {
      start.setTime(now.getTime());
    }

    const doctorIdInt = parseInt(doctorId);

    // Build where clause for templates
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    const whereClause: any = {
      doctor_id: doctorIdInt,
      is_available: true // Only fetch available templates
    };

    if (clinicId) {
      whereClause.clinic_id = parseInt(clinicId);
    }

    // Fetch doctor's availability templates
    const templates = await prisma.availability_templates.findMany({
      where: whereClause,
      include: {
        doctor: {
          select: {
            profile: {
              select: {
                display_name: true
              }
            }
          }
        },
        clinic: {
          select: {
            id: true,
            clinic_name: true,
            address: true,
            city: {
              select: {
                id: true,
                city_name: true,
                state_name: true,
              },
            },
          },
        },
      },
      orderBy: [
        { clinic_id: 'asc' },
        { day_of_week: 'asc' },
        { start_time: 'asc' }
      ]
    });

    if (templates.length === 0) {
      return NextResponse.json({
        success: true,
        data: {
          slots: [],
          grouped_by_date: {}
        }
      });
    }

    // Fetch doctor's unavailabilities for the date range
    // Convert date strings to DateTime objects for proper querying
    const startOfDay = new Date(start);
    startOfDay.setHours(0, 0, 0, 0);

    const endOfDay = new Date(end);
    endOfDay.setHours(23, 59, 59, 999);

    const unavailabilities = await prisma.unavailabilities.findMany({
      where: {
        doctor_id: doctorIdInt,
        date: {
          gte: startOfDay,
          lte: endOfDay
        }
      },
      orderBy: [
        { date: 'asc' },
        { start_time: 'asc' }
      ]
    });

    // Create a map of unavailabilities by date for quick lookup
    const unavailabilityMap = new Map<string, Array<typeof unavailabilities[0]>>();
    unavailabilities.forEach(unavailability => {
      const dateKey = unavailability.date instanceof Date
        ? unavailability.date.toISOString().split('T')[0]
        : unavailability.date;
      if (!unavailabilityMap.has(dateKey)) {
        unavailabilityMap.set(dateKey, []);
      }
      unavailabilityMap.get(dateKey)!.push(unavailability);
    });

    // Generate time slots from templates for the requested date range
    const generatedSlots = [];

    // Generate slots for each day in the requested range
    for (let currentDate = new Date(start); currentDate <= end; currentDate.setDate(currentDate.getDate() + 1)) {
      const dayOfWeek = currentDate.getDay(); // 0 = Sunday, 1 = Monday, etc.
      const dateKey = currentDate.toISOString().split('T')[0];

      // Check if doctor has any unavailabilities for this date
      const dayUnavailabilities = unavailabilityMap.get(dateKey) || [];

      // If doctor is unavailable for the full day, skip generating slots
      const hasFullDayUnavailability = dayUnavailabilities.some(unavailability =>
        unavailability.start_time === '00:00:00' && unavailability.end_time === '23:59:00'
      );

      if (hasFullDayUnavailability) {
        continue; // Skip this entire day
      }

      // Find templates for this day of week
      const dayTemplates = templates.filter(template => template.day_of_week === dayOfWeek);

      for (const template of dayTemplates) {
        // Parse time strings and create full datetime objects
        const [startHours, startMinutes] = template.start_time.split(':').map(Number);
        const [endHours, endMinutes] = template.end_time.split(':').map(Number);

        const startDateTime = new Date(currentDate);
        startDateTime.setHours(startHours, startMinutes, 0, 0);

        const endDateTime = new Date(currentDate);
        endDateTime.setHours(endHours, endMinutes, 0, 0);

        // Only include future time slots
        if (startDateTime >= now) {
          // Check if this time slot conflicts with any unavailabilities
          const hasConflict = dayUnavailabilities.some(unavailability => {
            const unavailStartTime = unavailability.start_time;
            const unavailEndTime = unavailability.end_time;
            const templateStartTime = template.start_time;
            const templateEndTime = template.end_time;

            // Check for time overlap
            return (
              (templateStartTime >= unavailStartTime && templateStartTime < unavailEndTime) ||
              (templateEndTime > unavailStartTime && templateEndTime <= unavailEndTime) ||
              (templateStartTime <= unavailStartTime && templateEndTime >= unavailEndTime)
            );
          });

          // Only add slot if there's no conflict with unavailabilities
          if (!hasConflict) {
            generatedSlots.push({
              doctor_id: template.doctor_id,
              clinic_id: template.clinic_id,
              date: new Date(currentDate),
              start_time: startDateTime,
              end_time: endDateTime,
              duration: template.duration,
              is_available: template.is_available,
              fee: template.fee ? Number(template.fee) : undefined,
              currency: template.currency || undefined,
              clinic: template.clinic
            });
          }
        }
      }
    }

    // Filter to only available slots
    const filteredSlots = generatedSlots.filter(slot => slot.is_available);

    // Transform to match the expected format
    const timeSlots = filteredSlots.map((slot, index) => ({
      id: index + 1, // Generate temporary IDs since we're not storing these
      date: slot.date,
      start_time: slot.start_time,
      end_time: slot.end_time,
      duration: slot.duration,
      fee: slot.fee,
      currency: slot.currency,
      clinic_id: slot.clinic_id,
      clinic: slot.clinic,
      doctor_name: templates[0]?.doctor.profile.display_name
    }));

    // Create exactly 7 days starting from the start date
    const dateRangeSlots = [];
    
    for (let i = 0; i < 7; i++) {
      const currentDate = new Date(start);
      currentDate.setDate(start.getDate() + i);
      const dateKey = currentDate.toISOString().split('T')[0];
      
      // Check if doctor is unavailable for this specific date
      const dayUnavailabilities = unavailabilityMap.get(dateKey) || [];
      const hasFullDayUnavailability = dayUnavailabilities.some(unavailability =>
        unavailability.start_time === '00:00:00' && unavailability.end_time === '23:59:00'
      );
      
      // Determine status based on unavailabilities
      const status = hasFullDayUnavailability ? 'unavailable' : 'available';
      
      // Find slots for this specific date
      const dateSlots = timeSlots.filter(slot => 
        slot.date.toISOString().split('T')[0] === dateKey
      );
      
      // Transform slots to frontend-friendly format
      const availableSlots = dateSlots.map(slot => ({
        id: slot.id,
        start_time: slot.start_time,
        end_time: slot.end_time,
        duration: slot.duration,
        fee: slot.fee,
        currency: slot.currency,
        clinic_id: slot.clinic_id,
        clinic: slot.clinic,
        doctor_name: slot.doctor_name
      }));
      
      dateRangeSlots.push({
        date: dateKey,
        status: status,
        available_slots: availableSlots
      });
    }

    return NextResponse.json({
      success: true,
      data: {
        date_range: {
          start_date: start.toISOString().split('T')[0],
          end_date: end.toISOString().split('T')[0]
        },
        total_slots: timeSlots.length,
        date_slots: serializePrismaResponse(dateRangeSlots)
      }
    });
  } catch (error) {
    console.error('Error fetching time slots from templates:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to fetch time slots' },
      { status: 500 }
    );
  }
}
