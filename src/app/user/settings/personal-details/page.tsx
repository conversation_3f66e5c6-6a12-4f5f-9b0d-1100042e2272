"use client";

import PersonalDetails from "@/components/shared/Settings/PersonalDetails/PersonalDetails";

export default function Page() {
  // Sample data - this would typically come from your API/database
  const sampleData = {
    fullName: "<PERSON><PERSON>",
    email: "<EMAIL>",
    phoneNumber: "90 4664 8873",
    dateOfBirth: "22.04.1994",
    gender: "Female",
    address: "H-43, Sector 63, Noida (201302)",
    maritalStatus: "Married",
    profileImage: "/assets/avatar.jpg",
  };

  const handleEditClick = () => {
    console.log("Edit clicked");
    // Handle edit functionality
  };

  const handleReplaceImage = () => {
    console.log("Replace image clicked");
    // Handle image replacement
  };

  const handleRemoveImage = () => {
    console.log("Remove image clicked");
    // Handle image removal
  };

  return (
    <div>
      <PersonalDetails
        data={sampleData}
        onEditClick={handleEditClick}
        onReplaceImage={handleReplaceImage}
        onRemoveImage={handleRemoveImage}
      />
    </div>
  );
}
