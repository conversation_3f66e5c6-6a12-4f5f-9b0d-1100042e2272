"use client";

import { useRouter } from "next/navigation";
import Navbar from "@/components/shared/Settings/Navbar/Navbar";
import { usePageHeader } from "@/contexts/PageHeaderContext";
import { useEffect } from "react";

export default function Layout({ children }: { children: React.ReactNode }) {
  const router = useRouter();
  const { setTitle, setSubtitle } = usePageHeader();

  useEffect(() => {
    setTitle("Settings");
    setSubtitle("View and edit your information");
  }, [setTitle, setSubtitle]);

  const handleNavItemClick = (itemId: string) => {
    const routeMap: Record<string, string> = {
      "personal-details": "/user/settings/personal-details",
      "medical-fertility-info": "/user/settings/medical-info",
      "change-password": "/user/settings/change-password",
      "help-support": "/user/settings/help-support",
      "notification-preferences": "/user/settings/notification",
      "delete-account": "/user/settings/delete-account",
    };

    const route = routeMap[itemId];
    if (route) {
      router.push(route);
    }
  };

  return (
    <div className="flex flex-1 gap-6 p-6">
      <Navbar onItemClick={handleNavItemClick} />
      <div className="flex-1">{children}</div>
    </div>
  );
}
