// Utility function to serialize Prisma responses (handle BigInt and Date objects)
// eslint-disable-next-line @typescript-eslint/no-explicit-any
export function serializePrismaResponse(data: any): any {
  if (data === null || data === undefined) {
    return data;
  }
  
  if (typeof data === 'bigint') {
    return data.toString();
  }
  
  if (data instanceof Date) {
    return data.toISOString();
  }
  
  if (Array.isArray(data)) {
    return data.map(serializePrismaResponse);
  }
  
  if (typeof data === 'object') {
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    const serialized: any = {};
    for (const [key, value] of Object.entries(data)) {
      serialized[key] = serializePrismaResponse(value);
    }
    return serialized;
  }
  
  return data;
}
