import React from "react";
import Card from "../../Card/Card";
import Button, { ButtonType } from "../../Button/Button";
import Avatar from "../../Avatar/Avatar";
import { TrashIcon, UploadIcon } from "@phosphor-icons/react";

interface PersonalDetailsData {
  fullName: string;
  email: string;
  phoneNumber: string;
  dateOfBirth: string;
  gender: string;
  address: string;
  maritalStatus: string;
  profileImage?: string;
}

interface PersonalDetailsProps {
  data: PersonalDetailsData;
  onEditClick?: () => void;
  onReplaceImage?: () => void;
  onRemoveImage?: () => void;
  className?: string;
}

const PersonalDetails: React.FC<PersonalDetailsProps> = ({
  data,
  onEditClick,
  onReplaceImage,
  onRemoveImage,
  className = "",
}) => {
  return (
    <div className={`py-0`}>
      {/* Header */}
      <div className="flex justify-between items-center">
        <h1 className="text-2xl font-bold text-[var(--grey-7)]">
          Personal Details
        </h1>
        <div>
          <Button
            type={ButtonType.PRIMARY}
            text="Edit Details"
            onClick={onEditClick}
          />
        </div>
      </div>

      {/* Profile Section */}
      <Card className="p-8">
        <div className="space-y-8">
          {/* Profile Image Section */}
          <div className="flex flex-col items-center space-y-4">
            <Avatar
              src={data.profileImage || "/assets/avatar.jpg"}
              alt={data.fullName}
              width={120}
              height={120}
            />

            {/* Image Action Buttons */}
            <div className="flex gap-3">
              <button
                onClick={onReplaceImage}
                className="flex items-center gap-2 px-4 py-2 text-[var(--grey-7)] bg-[var(--grey-2)] hover:bg-[var(--grey-3)] rounded-lg transition-colors duration-200"
              >
                <UploadIcon size={16} />
                <span className="text-sm font-medium">Replace</span>
              </button>
              <button
                onClick={onRemoveImage}
                className="flex items-center gap-2 px-4 py-2 text-[var(--grey-7)] bg-[var(--grey-2)] hover:bg-[var(--grey-3)] rounded-lg transition-colors duration-200"
              >
                <TrashIcon size={16} />
                <span className="text-sm font-medium">Remove</span>
              </button>
            </div>
          </div>

          {/* User Information Grid */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-x-12 gap-y-6">
            {/* Full Name */}
            <div>
              <label className="block text-[var(--grey-6)] text-sm font-medium mb-2">
                Full Name
              </label>
              <p className="text-[var(--grey-7)] text-base font-medium">
                {data.fullName}
              </p>
            </div>

            {/* Email */}
            <div>
              <label className="block text-[var(--grey-6)] text-sm font-medium mb-2">
                Email
              </label>
              <p className="text-[var(--grey-7)] text-base font-medium">
                {data.email}
              </p>
            </div>

            {/* Phone Number */}
            <div>
              <label className="block text-[var(--grey-6)] text-sm font-medium mb-2">
                Phone Number
              </label>
              <p className="text-[var(--grey-7)] text-base font-medium">
                {data.phoneNumber}
              </p>
            </div>

            {/* Date of Birth */}
            <div>
              <label className="block text-[var(--grey-6)] text-sm font-medium mb-2">
                Date of Birth
              </label>
              <p className="text-[var(--grey-7)] text-base font-medium">
                {data.dateOfBirth}
              </p>
            </div>

            {/* Gender */}
            <div>
              <label className="block text-[var(--grey-6)] text-sm font-medium mb-2">
                Gender
              </label>
              <p className="text-[var(--grey-7)] text-base font-medium">
                {data.gender}
              </p>
            </div>

            {/* Marital Status */}
            <div>
              <label className="block text-[var(--grey-6)] text-sm font-medium mb-2">
                Marital Status
              </label>
              <p className="text-[var(--grey-7)] text-base font-medium">
                {data.maritalStatus}
              </p>
            </div>

            {/* Address - Full Width */}
            <div className="md:col-span-2">
              <label className="block text-[var(--grey-6)] text-sm font-medium mb-2">
                Address
              </label>
              <p className="text-[var(--grey-7)] text-base font-medium">
                {data.address}
              </p>
            </div>
          </div>
        </div>
      </Card>
    </div>
  );
};

export default PersonalDetails;
