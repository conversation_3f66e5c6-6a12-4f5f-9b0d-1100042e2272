import { <PERSON><PERSON>, <PERSON>Obj } from "@storybook/nextjs";
import Doctor<PERSON><PERSON> from "./DoctorCard";

const meta: Meta<typeof DoctorCard> = {
  title: "Components/Appointments/DoctorCard",
  component: Doctor<PERSON><PERSON>,
  tags: ["autodocs"],
  parameters: {
    layout: "centered",
  },
};

export default meta;

type Story = StoryObj<typeof DoctorCard>;

export const Default: Story = {
  args: {
    imageUrl: "https://randomuser.me/api/portraits/women/44.jpg",
    name: "Dr. <PERSON><PERSON><PERSON>",
    title: "Fertility Consultant, Obstetrician",
    experience: "9+ years",
    onClick: () => console.log("Doctor card clicked"),
    selected: false,
  },
};

export const Selected: Story = {
  args: {
    imageUrl: "https://randomuser.me/api/portraits/women/44.jpg",
    name: "Dr. <PERSON><PERSON><PERSON>",
    title: "Fertility Consultant, Obstetrician",
    experience: "9+ years",
    onClick: () => console.log("Doctor card clicked"),
    selected: true,
  },
};
