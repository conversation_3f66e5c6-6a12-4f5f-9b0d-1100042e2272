import { useScreenWidth } from "@/hooks/useScreenWidth";
import { BriefcaseIcon, MoneyWavyIcon } from "@phosphor-icons/react";
import Image from "next/image";
import React from "react";
import Card from "../Card";

interface DoctorCardProps {
  imageUrl: string;
  name: string;
  title: string;
  experience: string;
  consultationFee: number;
  onClick: () => void;
  className?: string;
  selected?: boolean;
}

const DoctorCard: React.FC<DoctorCardProps> = ({
  imageUrl,
  name,
  title,
  experience,
  consultationFee,
  onClick,
  className,
  selected = false,
}) => {
  const screenWidth = useScreenWidth();
  return (
    <Card
      className={`py-5 md:py-10 px-4 md:px-5 flex flex-col gap-4 items-center w-[174px] md:w-[290px] cursor-pointer hover:shadow-lg transition-all duration-300 ${
        selected
          ? "border-[var(--violet-11)] shadow-lg shadow-[#DCDCE899]"
          : "border-[var(--grey-3)]"
      } ${className}`}
      onClick={onClick}
    >
      <Image
        src={imageUrl}
        alt={name}
        width={screenWidth < 768 ? 90 : 154}
        height={screenWidth < 768 ? 90 : 154}
        className="rounded-full object-cover w-[90px] h-[90px] md:w-[154px] md:h-[154px]"
      />
      <div className="font-bold text-lg md:text-xl text-[var(--grey-7)] text-center">
        {name}
      </div>
      <div className="flex flex-col text-sm md:text-base font-medium gap-2">
        <div className="text-[var(--grey-6)] text-center">{title}</div>
        <div className="flex justify-center items-center text-[var(--grey-6)] gap-2">
          <BriefcaseIcon
            size={16}
            className="!text-[var(--violet-11)]"
            weight="bold"
          />
          {experience}
        </div>
        <div className="flex justify-center items-center text-[var(--grey-6)] gap-2">
          <MoneyWavyIcon
            size={16}
            className="!text-[var(--violet-11)]"
            weight="bold"
          />
          ₹{consultationFee}
        </div>
      </div>
    </Card>
  );
};

export default DoctorCard;
