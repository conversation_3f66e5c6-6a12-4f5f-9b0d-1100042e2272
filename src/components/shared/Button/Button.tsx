import React from "react";

export enum ButtonType {
  PRIMARY = "primary",
  SECONDARY = "secondary",
}

export enum ButtonSubmitType {
  SUBMIT = "submit",
  BUTTON = "button",
}

export interface ButtonProps {
  type: ButtonType;
  submitType?: ButtonSubmitType;
  text?: string;
  icon?: React.ReactNode;
  onClick?: () => void;
  disabled?: boolean;
  className?: string;
  size?: "sm" | "default" | "lg";
}

const Button: React.FC<ButtonProps> = ({
  type,
  submitType = ButtonSubmitType.BUTTON,
  text,
  icon,
  onClick,
  disabled = false,
  className = "",
  size = "default",
}) => {
  const baseClasses =
    "w-full flex items-center justify-center gap-2 font-medium transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed rounded-[0.25rem] cursor-pointer";

  const typeClasses = {
    [ButtonType.PRIMARY]:
      "bg-[var(--red-6)] text-white hover:bg-[var(--red-7)] border-0",
    [ButtonType.SECONDARY]:
      "bg-white text-[var(--darker-grey)] border-[1px] border-[var(--light-grey)] hover:bg-[var(--light-grey)] hover:border-[var(--light-grey)] focus:ring-[var(--light-grey)]",
  };

  const sizeClasses = {
    sm: "px-4 py-2 text-sm h-10",
    default: "px-7.5 py-3.5 text-base h-[3.125rem]",
    lg: "px-[1.875rem] py-[0.875rem] text-xl h-16",
  };

  const buttonClasses = `${baseClasses} ${typeClasses[type]} ${sizeClasses[size]} ${className}`;

  return (
    <button
      type={submitType}
      className={buttonClasses}
      onClick={onClick}
      disabled={disabled}
    >
      {text}
      {icon && <span className={`${text ? "ml-2" : ""}`}>{icon}</span>}
    </button>
  );
};

export default Button;
