import { <PERSON><PERSON>, StoryObj } from "@storybook/nextjs";
import DoctorP<PERSON><PERSON>leC<PERSON> from "./DoctorProfileCard";

const meta: Meta<typeof DoctorProfileCard> = {
  title: "Components/Appointments/DoctorProfileCard",
  component: DoctorProfileCard,
  tags: ["autodocs"],
  parameters: {
    layout: "centered",
  },
};

export default meta;

type Story = StoryObj<typeof DoctorProfileCard>;

export const Default: Story = {
  args: {
    imageUrl: "https://randomuser.me/api/portraits/women/44.jpg",
    name: "Dr. <PERSON><PERSON>",
    specialization: "IVF Specialist, Gynaecologist",
    experience: "12+ years",
    clinicLocation: "Gunjan IVF Clinic, Indrapuram Ghaziabad",
    consultationFee: 1200,
    consultationType: "In-Person Consultation",
    bio: "Dr. <PERSON><PERSON> is a senior IVF specialist with over 12 years of experience in reproductive medicine and women's health. She has successfully treated over 500 couples and is known for compassionate and personalized care. Her expertise includes IVF, IUI, PCOS management, and high-risk pregnancies.",
  },
};
