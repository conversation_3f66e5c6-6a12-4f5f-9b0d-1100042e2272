import React, { useEffect, useState } from "react";
import StepHeader from "@/components/shared/StepHeader/StepHeader";
import Button, { ButtonType } from "@/components/shared/Button/Button";
import ToggleButton from "@/components/shared/ToggleButton/ToggleButton";
import { useCities } from "@/hooks/useCities";
import { ArrowLeftIcon } from "@phosphor-icons/react";
import { usePageHeader } from "@/contexts/PageHeaderContext";
import { useBreadcrumb } from "@/contexts/BreadcrumbContext";

export interface SelectCityProps {
  onCitySelect: (cityId: number, cityName: string) => void;
  onBack: () => void;
  selectedCityId?: number;
}

const SelectCity: React.FC<SelectCityProps> = ({
  onCitySelect,
  onBack,
  selectedCityId,
}) => {
  const cities = useCities();
  const [selectedCity, setSelectedCity] = useState<number | undefined>(
    selectedCityId
  );
  const { setTitle, setSubtitle } = usePageHeader();
  const { setBreadcrumbs } = useBreadcrumb();

  useEffect(() => {
    setTitle(null);
    setSubtitle(null);
    setBreadcrumbs([
      { label: "My Appointments", href: "/user/appointments" },
      { label: "Schedule Appointments", isActive: true },
    ]);
  }, [setTitle, setSubtitle]);

  const handleCitySelect = (cityId: number, cityName: string) => {
    setSelectedCity(cityId);
    onCitySelect(cityId, cityName);
  };

  return (
    <div className="w-full xl:w-[33.125rem] flex flex-col items-center gap-8 md:gap-12">
      <div className="flex flex-col items-center gap-2">
        <StepHeader
          currentStep={1}
          totalSteps={4}
          title="Select City"
          className="!mb-0"
        />

        <p className="text-center text-[var(--grey-6)] text-base font-medium">
          Choose your city to view available clinics
        </p>
      </div>

      <div className="flex flex-col gap-8 md:gap-12">
        <div className="flex flex-col gap-2">
          <label className="text-[var(--grey-6)] text-base font-medium text-left">
            Select City
          </label>

          <div className="grid grid-cols-2 md:grid-cols-3 gap-8">
            {cities.map((city) => (
              <ToggleButton
                key={city.cityId}
                isSelected={selectedCity === city.cityId}
                onClick={() => handleCitySelect(city.cityId, city.city)}
                variant="default"
                className="!py-3.5 !px-6"
              >
                {city.city}
              </ToggleButton>
            ))}
          </div>
        </div>

        <Button
          type={ButtonType.SECONDARY}
          text="Back"
          icon={<ArrowLeftIcon size={20} />}
          onClick={onBack}
          className="!flex !flex-row-reverse"
        />
      </div>
    </div>
  );
};

export default SelectCity;
