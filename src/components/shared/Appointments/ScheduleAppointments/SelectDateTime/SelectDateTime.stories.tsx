import type { <PERSON><PERSON>, StoryObj } from "@storybook/nextjs";
import SelectDateTime from "./SelectDateTime";
import { PageHeaderProvider } from "@/contexts/PageHeaderContext";
import { BreadcrumbProvider } from "@/contexts/BreadcrumbContext";

const meta: Meta<typeof SelectDateTime> = {
  title: "Components/Appointments/ScheduleAppointments/SelectDateTime",
  component: SelectDateTime,
  parameters: {
    layout: "centered",
    docs: {
      description: {
        component:
          "A component for selecting date and time in the appointment scheduling flow.",
      },
    },
  },
  argTypes: {
    onDateTimeSelect: { action: "date time selected" },
    onBack: { action: "back clicked" },
    onCancel: { action: "cancel clicked" },
    onConfirm: { action: "confirm clicked" },
    selectedDate: {
      control: { type: "text" },
      description: "Pre-selected date (YYYY-MM-DD format)",
    },
    selectedTime: {
      control: { type: "select" },
      options: [
        undefined,
        "10:00 am",
        "10:30 am",
        "11:30 am",
        "12:00 pm",
        "12:30 pm",
        "03:00 pm",
        "03:30 pm",
        "04:30 pm",
        "05:00 pm",
        "05:30 pm",
        "06:30 pm",
        "07:00 am",
        "07:30 pm",
        "08:30 am",
      ],
      description: "Pre-selected time",
    },
  },
  decorators: [
    (Story) => (
      <PageHeaderProvider>
        <BreadcrumbProvider>
          <div className="w-full max-w-4xl">
            <Story />
          </div>
        </BreadcrumbProvider>
      </PageHeaderProvider>
    ),
  ],
};

export default meta;
type Story = StoryObj<typeof SelectDateTime>;

export const Default: Story = {
  args: {
    selectedDate: undefined,
    selectedTime: undefined,
  },
};

export const WithPreselectedDateTime: Story = {
  args: {
    selectedDate: new Date().toISOString().split("T")[0], // Today
    selectedTime: "03:30 pm",
  },
};

export const WithDateOnly: Story = {
  args: {
    selectedDate: new Date().toISOString().split("T")[0], // Today
    selectedTime: undefined,
  },
};

export const WithTimeOnly: Story = {
  args: {
    selectedDate: undefined,
    selectedTime: "10:00 am",
  },
};

export const Interactive: Story = {
  args: {
    selectedDate: undefined,
    selectedTime: undefined,
  },
  parameters: {
    docs: {
      description: {
        story:
          "Interactive version where you can click on dates and times to see the selection behavior.",
      },
    },
  },
};
