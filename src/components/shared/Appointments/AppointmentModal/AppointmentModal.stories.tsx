import React, { useState } from "react";
import AppointmentModal, { AppointmentModalType } from "./AppointmentModal";
import Button, { ButtonType } from "../../Button/Button";

export default {
  title: "Components/Appointments/AppointmentModal",
  component: AppointmentModal,
};

const mockAppointmentData = {
  doctorImageUrl: "/assets/avatar.jpg",
  doctorName: "Dr. <PERSON><PERSON>",
  clinicName: "Gunjan IVF Clinic",
  clinicLocation: "Indrapuram Ghaziabad",
  appointmentDate: "15 July 2025",
  appointmentTime: "10:00 AM - 10:30 AM",
  consultationFee: 1200,
};

export const ConfirmAppointment = () => {
  const [isOpen, setIsOpen] = useState(false);

  return (
    <div style={{ padding: "20px" }}>
      <Button
        type={ButtonType.PRIMARY}
        text="Open Confirm Modal"
        onClick={() => setIsOpen(true)}
      />
      <AppointmentModal
        isOpen={isOpen}
        type={AppointmentModalType.CONFIRM}
        appointmentData={mockAppointmentData}
        onClose={() => setIsOpen(false)}
        onConfirm={() => {
          alert("Appointment confirmed!");
          setIsOpen(false);
        }}
        onBack={() => setIsOpen(false)}
      />
    </div>
  );
};

export const ConfirmedAppointment = () => {
  const [isOpen, setIsOpen] = useState(false);

  return (
    <div style={{ padding: "20px" }}>
      <Button
        type={ButtonType.PRIMARY}
        text="Open Confirmed Modal"
        onClick={() => setIsOpen(true)}
      />
      <AppointmentModal
        isOpen={isOpen}
        type={AppointmentModalType.CONFIRMED}
        appointmentData={mockAppointmentData}
        onClose={() => setIsOpen(false)}
      />
    </div>
  );
};

export const CancelAppointment = () => {
  const [isOpen, setIsOpen] = useState(false);

  return (
    <div style={{ padding: "20px" }}>
      <Button
        type={ButtonType.PRIMARY}
        text="Open Cancel Modal"
        onClick={() => setIsOpen(true)}
      />
      <AppointmentModal
        isOpen={isOpen}
        type={AppointmentModalType.CANCEL}
        appointmentData={mockAppointmentData}
        onClose={() => setIsOpen(false)}
        onCancel={() => {
          alert("Appointment cancelled!");
          setIsOpen(false);
        }}
        onBack={() => setIsOpen(false)}
      />
    </div>
  );
};

export const AllVariants = () => {
  const [confirmOpen, setConfirmOpen] = useState(false);
  const [confirmedOpen, setConfirmedOpen] = useState(false);
  const [cancelOpen, setCancelOpen] = useState(false);

  return (
    <div
      style={{
        padding: "20px",
        display: "flex",
        gap: "16px",
        flexWrap: "wrap",
      }}
    >
      <Button
        type={ButtonType.PRIMARY}
        text="Confirm Modal"
        onClick={() => setConfirmOpen(true)}
      />
      <Button
        type={ButtonType.PRIMARY}
        text="Confirmed Modal"
        onClick={() => setConfirmedOpen(true)}
      />
      <Button
        type={ButtonType.SECONDARY}
        text="Cancel Modal"
        onClick={() => setCancelOpen(true)}
      />

      <AppointmentModal
        isOpen={confirmOpen}
        type={AppointmentModalType.CONFIRM}
        appointmentData={mockAppointmentData}
        onClose={() => setConfirmOpen(false)}
        onConfirm={() => {
          setConfirmOpen(false);
          setConfirmedOpen(true);
        }}
        onBack={() => setConfirmOpen(false)}
      />

      <AppointmentModal
        isOpen={confirmedOpen}
        type={AppointmentModalType.CONFIRMED}
        appointmentData={mockAppointmentData}
        onClose={() => setConfirmedOpen(false)}
      />

      <AppointmentModal
        isOpen={cancelOpen}
        type={AppointmentModalType.CANCEL}
        appointmentData={mockAppointmentData}
        onClose={() => setCancelOpen(false)}
        onCancel={() => {
          alert("Appointment cancelled!");
          setCancelOpen(false);
        }}
        onBack={() => setCancelOpen(false)}
      />
    </div>
  );
};
