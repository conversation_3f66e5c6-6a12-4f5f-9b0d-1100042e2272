import React from "react";
import Card from "../../Card";
import Button, { ButtonType } from "../../Button/Button";
import Image from "next/image";
import Avatar from "../../Avatar/Avatar";
import {
  CalendarCheckIcon,
  ClockIcon,
  MoneyWavyIcon,
} from "@phosphor-icons/react";
import { useScreenWidth } from "@/hooks/useScreenWidth";

export enum AppointmentModalType {
  CONFIRM = "confirm",
  CONFIRMED = "confirmed",
  CANCEL = "cancel",
}

interface AppointmentData {
  doctorImageUrl: string;
  doctorName: string;
  clinicName: string;
  clinicLocation: string;
  appointmentDate: string;
  appointmentTime: string;
  consultationFee: number;
}

interface AppointmentModalProps {
  isOpen: boolean;
  type: AppointmentModalType;
  appointmentData: AppointmentData;
  onClose: () => void;
  onConfirm?: () => void;
  onCancel?: () => void;
  onBack?: () => void;
}

const AppointmentModal: React.FC<AppointmentModalProps> = ({
  isOpen,
  type,
  appointmentData,
  onClose,
  onConfirm,
  onCancel,
  onBack,
}) => {
  const screenWidth = useScreenWidth();
  if (!isOpen) return null;

  const getModalConfig = () => {
    switch (type) {
      case AppointmentModalType.CONFIRM:
        return {
          iconSrc: "/assets/modal/modalConfirm.svg",
          title: "Confirm Appointment",
          description: `Are you sure you want to schedule appointment with ${appointmentData.doctorName}?`,
          primaryButtonText: "Confirm Now",
          primaryAction: onConfirm,
          secondaryButtonText: "Back",
          secondaryAction: onBack || onClose,
          showReminderText: false,
        };
      case AppointmentModalType.CONFIRMED:
        return {
          iconSrc: "/assets/modal/modalConfirmed.svg",
          title: "Appointment Confirmed 🎉",
          description:
            "You will receive a reminder via SMS prior to the appointment",
          primaryButtonText: "Back to My Appointment",
          primaryAction: onClose,
          secondaryButtonText: null,
          secondaryAction: null,
          showReminderText: true,
        };
      case AppointmentModalType.CANCEL:
        return {
          iconSrc: "/assets/modal/modalCancel.svg",
          title: "Cancel Appointment",
          description: `Are you sure you want to cancel appointment with ${appointmentData.doctorName}`,
          primaryButtonText: "Cancel Appointment",
          primaryAction: onCancel,
          secondaryButtonText: "Back",
          secondaryAction: onBack || onClose,
          showReminderText: false,
        };
      default:
        return null;
    }
  };

  const config = getModalConfig();
  if (!config) return null;

  return (
    <div
      className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4"
      onClick={onClose}
    >
      <div className="w-full max-w-md" onClick={(e) => e.stopPropagation()}>
        <Card className="xl:w-[43.69rem] pt-12.5 pb-15 px-5 xl:px-20 relative">
          {/* Content */}
          <div className="relative z-10 text-center h-full flex flex-col justify-center gap-10">
            {/* Icon */}
            <div className="flex justify-center">
              <Image
                src={config.iconSrc}
                alt={config.title}
                width={screenWidth < 768 ? 126 : 156}
                height={screenWidth < 768 ? 103 : 127.5}
              />
            </div>

            <div className="h-full flex flex-col justify-center gap-4">
              {/* Title */}
              <h2 className="text-2xl font-bold text-[var(--grey-7)]">
                {config.title}
              </h2>

              <div className="flex flex-col-reverse md:flex-col gap-4">
                {/* Appointment Details Card */}
                {type !== AppointmentModalType.CANCEL && (
                  <Card className="w-full p-5 md:px-7.5 flex flex-col justify-center items-center">
                    {/* Doctor Info */}
                    <div className="flex justify-center gap-3 mb-4">
                      <Avatar
                        src={appointmentData.doctorImageUrl}
                        alt={appointmentData.doctorName}
                        width={35}
                        height={35}
                      />
                      <div className="text-left">
                        <h3 className="font-bold text-[var(--grey-7)] text-xl">
                          {appointmentData.doctorName}
                        </h3>
                        <p className="text-[var(--grey-6)] text-base font-medium">
                          {appointmentData.clinicName},{" "}
                          {appointmentData.clinicLocation}
                        </p>
                      </div>
                    </div>

                    {/* Appointment Details */}
                    <div className="space-y-3">
                      <div className="flex items-center gap-2">
                        <CalendarCheckIcon
                          size={16}
                          className="text-[var(--violet-11)]"
                        />
                        <span className="text-[var(--grey-7)] text-base font-medium whitespace-nowrap">
                          {appointmentData.appointmentDate}
                        </span>
                        <div className="w-px h-4 bg-gray-300 mx-2"></div>
                        <ClockIcon
                          size={16}
                          className="text-[var(--violet-11)]"
                        />
                        <span className="text-[var(--grey-7)] text-base font-medium whitespace-nowrap">
                          {appointmentData.appointmentTime}
                        </span>
                      </div>
                      <div className="flex items-center gap-2">
                        <MoneyWavyIcon
                          size={16}
                          className="text-[var(--violet-11)]"
                        />
                        <span className="text-[var(--grey-6)] text-base font-medium">
                          ₹{appointmentData.consultationFee} Consultation fee at
                          clinic
                        </span>
                      </div>
                    </div>
                  </Card>
                )}

                {/* Description */}
                <p className="text-[var(--grey-6)] text-base font-medium text-center">
                  {config.description}
                </p>
              </div>
            </div>

            {/* Action Buttons */}
            <div
              className={`flex flex-col-reverse md:flex-row gap-4 ${config.secondaryButtonText ? "justify-between" : "justify-center"}`}
            >
              {config.secondaryButtonText && (
                <div className="flex-1">
                  <Button
                    type={ButtonType.SECONDARY}
                    text={config.secondaryButtonText}
                    onClick={config.secondaryAction!}
                    className="w-full"
                  />
                </div>
              )}
              <div className="flex-1">
                <Button
                  type={ButtonType.PRIMARY}
                  text={config.primaryButtonText}
                  onClick={config.primaryAction!}
                  className="w-full"
                />
              </div>
            </div>
          </div>
        </Card>
      </div>
    </div>
  );
};

export default AppointmentModal;
