"use client";
import React, { useState } from "react";
import { format, isSameDay, addMonths, subMonths } from "date-fns";
import { ChevronLeft, ChevronRight, Calendar as CalendarIcon, Clock, MapPin, FileText } from "lucide-react";
import ReactCalendar from "react-calendar";
import { But<PERSON> } from "@/components/ShadcnUI/button";
import { Card, CardContent } from "@/components/ShadcnUI/card";
import { Dialog, DialogContent, DialogHeader, DialogTitle } from "@/components/ShadcnUI/dialog";

interface Unavailability {
  id: number;
  doctor_id: number;
  clinic_id?: number;
  date: string;
  start_time: string;
  end_time: string;
  duration: number;
  reason?: string;
  notes?: string;
  clinic?: {
    id: number;
    clinic_name: string;
    address?: string;
  };
}

interface UnavailabilityCalendarProps {
  unavailabilities: Unavailability[];
  onMonthChange?: (date: Date) => void;
}

export default function UnavailabilityCalendar({
  unavailabilities,
  onMonthChange
}: UnavailabilityCalendarProps) {
  const [currentDate, setCurrentDate] = useState(new Date());
  const [selectedDate, setSelectedDate] = useState<Date | null>(null);
  const [showDetails, setShowDetails] = useState(false);

  const navigateMonth = (direction: 'prev' | 'next') => {
    const newDate = direction === 'prev' ? subMonths(currentDate, 1) : addMonths(currentDate, 1);
    setCurrentDate(newDate);
    onMonthChange?.(newDate);
  };

  const getUnavailabilitiesForDate = (date: Date) => {
    return unavailabilities.filter(unavailability =>
      isSameDay(new Date(unavailability.date), date)
    );
  };



  const formatTime = (time: string) => {
    const [hours, minutes] = time.split(':');
    const hour = parseInt(hours);
    const ampm = hour >= 12 ? 'PM' : 'AM';
    const displayHour = hour === 0 ? 12 : hour > 12 ? hour - 12 : hour;
    return `${displayHour}:${minutes} ${ampm}`;
  };

  const isFullDayUnavailable = (unavailabilities: Unavailability[]) => {
    return unavailabilities.some(u => u.start_time === "00:00" && u.end_time === "23:59");
  };

  const selectedDateUnavailabilities = selectedDate ? getUnavailabilitiesForDate(selectedDate) : [];

  return (
    <div className="space-y-4 p-8">
      {/* Calendar Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2">
          <CalendarIcon className="h-5 w-5 text-blue-600" />
          <h3 className="text-lg font-semibold">
            {format(currentDate, 'MMMM yyyy')}
          </h3>
        </div>
        <div className="flex items-center gap-1">
          <Button
            variant="outline"
            size="sm"
            onClick={() => navigateMonth('prev')}
          >
            <ChevronLeft className="h-4 w-4" />
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={() => navigateMonth('next')}
          >
            <ChevronRight className="h-4 w-4" />
          </Button>
        </div>
      </div>

      {/* Calendar */}
      <ReactCalendar
        className="w-full border-0 [&_.react-calendar__navigation]:hidden [&_.react-calendar__viewContainer]:w-full [&_.react-calendar__month-view]:w-full [&_.react-calendar__month-view__days]:w-full [&_.react-calendar__tile]:w-full [&_.react-calendar__tile]:h-12 [&_.react-calendar__tile]:flex [&_.react-calendar__tile]:items-center [&_.react-calendar__tile]:justify-center [&_.react-calendar__tile]:relative"
        view="month"
        activeStartDate={currentDate}
        onActiveStartDateChange={({ activeStartDate }) => {
          if (activeStartDate) {
            setCurrentDate(activeStartDate);
            onMonthChange?.(activeStartDate);
          }
        }}
        onClickDay={(value) => {
          const date = value as Date;
          const dayUnavailabilities = getUnavailabilitiesForDate(date);
          if (dayUnavailabilities.length > 0) {
            setSelectedDate(date);
            setShowDetails(true);
          }
        }}
        tileClassName={({ date, view }) => {
          if (view !== 'month') return undefined;
          const dayUnavailabilities = getUnavailabilitiesForDate(date);
          if (dayUnavailabilities.length === 0) return undefined;
          const full = isFullDayUnavailable(dayUnavailabilities);
          return full ? '!bg-red-500 text-white hover:!bg-red-600' : '!bg-red-200 text-red-800 hover:!bg-red-300';
        }}
        tileContent={({ date, view }) => {
          if (view !== 'month') return null;
          const dayUnavailabilities = getUnavailabilitiesForDate(date);
          if (dayUnavailabilities.length === 0) return null;
          const full = isFullDayUnavailable(dayUnavailabilities);
          return (
            <div className="absolute bottom-1 right-1">
              <div className={`w-2 h-2 rounded-full ${full ? 'bg-white' : 'bg-red-600'}`} />
            </div>
          );
        }}
      />

      {/* Legend */}
      <div className="flex items-center justify-center gap-6 text-sm">
        <div className="flex items-center gap-2">
          <div className="w-4 h-4 bg-red-200 rounded border" />
          <span>Partial Day Unavailable</span>
        </div>
        <div className="flex items-center gap-2">
          <div className="w-4 h-4 bg-red-500 rounded border" />
          <span>Full Day Unavailable</span>
        </div>
      </div>

      {/* Details Dialog */}
      <Dialog open={showDetails} onOpenChange={setShowDetails}>
        <DialogContent className="max-w-md">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <CalendarIcon className="h-5 w-5" />
              Unavailabilities for {selectedDate && format(selectedDate, 'EEEE, MMMM d, yyyy')}
            </DialogTitle>
          </DialogHeader>

          <div className="space-y-4">
            {selectedDateUnavailabilities.map((unavailability) => (
              <Card key={unavailability.id} className="border-l-4 border-l-red-500">
                <CardContent className="p-4 space-y-3">
                  {/* Time */}
                  <div className="flex items-center gap-2">
                    <Clock className="h-4 w-4 text-gray-500" />
                    <span className="font-medium">
                      {unavailability.start_time === "00:00" && unavailability.end_time === "23:59"
                        ? "Full Day"
                        : `${formatTime(unavailability.start_time)} - ${formatTime(unavailability.end_time)}`
                      }
                    </span>
                  </div>

                  {/* Clinic */}
                  {unavailability.clinic && (
                    <div className="flex items-center gap-2">
                      <MapPin className="h-4 w-4 text-gray-500" />
                      <span className="text-sm">{unavailability.clinic.clinic_name}</span>
                    </div>
                  )}

                  {/* Reason */}
                  {unavailability.reason && (
                    <div className="flex items-start gap-2">
                      <FileText className="h-4 w-4 text-gray-500 mt-0.5" />
                      <div>
                        <div className="text-sm font-medium text-gray-700">Reason:</div>
                        <div className="text-sm text-gray-600">{unavailability.reason}</div>
                      </div>
                    </div>
                  )}

                  {/* Notes */}
                  {unavailability.notes && (
                    <div className="text-sm text-gray-600 bg-gray-50 p-2 rounded">
                      <strong>Notes:</strong> {unavailability.notes}
                    </div>
                  )}
                </CardContent>
              </Card>
            ))}
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
}
