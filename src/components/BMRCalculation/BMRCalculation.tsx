import React, { useState, useEffect } from "react";
import { useQuery } from "@tanstack/react-query";
import { Card } from "../ShadcnUI/card";
import { usePageHeader } from "@/contexts/PageHeaderContext";
import Image from "next/image";
import Button, { ButtonType } from "@/components/shared/Button/Button";
import { DownloadSimpleIcon } from "@phosphor-icons/react";
import Modal from "../shared/Modal/Modal";
import WarningAlert from "../shared/WarningAlert/WarningAlert";
import { useAuth } from "@/contexts/AuthContext";
import PageLoader from "../shared/PageLoader";
import { useRouter } from "next/navigation";
import { Loader } from "lucide-react";
import { clearFormData } from "@/utils/formLocalStorage";

// Custom hook for BMR data
// eslint-disable-next-line @typescript-eslint/no-explicit-any
const useBMRData = (user: any) => {
  const { user_token } = useAuth();
  const router = useRouter();

  return useQuery({
    queryKey: ["bmr"],
    queryFn: async () => {
      const response = await fetch("/api/v1/fertility-diet-plan/results", {
        method: "GET",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${user_token}`,
        },
      });

      // check if diet plan score not exist in db, redirect to `user/fertility-diet-plan/diet-assessment` page
      if (response.status === 404) {
        router.push("/user/fertility-diet-plan/diet-assessment");
        return;
      }

      if (!response.ok) {
        throw new Error("Failed to fetch BMR data");
      }
      return response.json();
    },
    enabled: !!user,
  });
};

const BMRCalculation = () => {
  const { setTitle, setSubtitle } = usePageHeader();
  const [modalOpen, setModalOpen] = useState(false);
  const [showWarningAlert, setShowWarningAlert] = useState(false);
  const [showDownloadConfirmation, setShowDownloadConfirmation] =
    useState(false);
  const { user, user_token } = useAuth();
  const router = useRouter();
  const [isDeleting, setIsDeleting] = useState(false);
  const [isDownloading, setIsDownloading] = useState(false);
  const { data, isLoading, error } = useBMRData(user);
  const bmr = data?.bmr;
  const targetCalories = data?.targetCalories;
  const mealPlan = data?.mealPlan;

  const handleRetakeAssessment = async () => {
    setIsDeleting(true);
    try {
      const response = await fetch("/api/v1/fertility-diet-plan/form", {
        method: "DELETE",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${user_token}`,
        },
      });

      if (!response.ok) {
        throw new Error("Failed to delete assessment");
      }

      router.push("/user/fertility-diet-plan/diet-assessment");
      clearFormData("diet-plan", user?.id || "");
    } catch (error) {
      console.error(error);
      setIsDeleting(false);
    }
  };

  const handleWarningCancel = () => {
    setShowWarningAlert(false);
  };

  const handleWarningProceed = () => {
    setShowWarningAlert(false);
    handleRetakeAssessment();
  };

  const handleRetakeAssessmentClick = () => {
    setShowWarningAlert(true);
  };

  const handleDownloadConfirmation = () => {
    setShowDownloadConfirmation(true);
  };

  const handleDownloadCancel = () => {
    setShowDownloadConfirmation(false);
  };

  const handleDownloadProceed = () => {
    setShowDownloadConfirmation(false);
    handleDownloadMealPlan();
  };

  const handleDownloadMealPlan = async () => {
    if (!mealPlan?.mealPlanDocUrl) {
      alert(
        "No meal plan document available for download. Please contact our nutrition team for a personalized meal plan."
      );
      return;
    }

    setIsDownloading(true);
    try {
      // Fetch the PDF file as a blob
      const response = await fetch(mealPlan.mealPlanDocUrl);
      if (!response.ok) {
        throw new Error("Failed to fetch PDF");
      }

      const blob = await response.blob();

      // Create a blob URL and trigger download
      const blobUrl = window.URL.createObjectURL(blob);
      const link = document.createElement("a");
      link.href = blobUrl;
      link.download = `meal-plan-${mealPlan.condition.toLowerCase()}.pdf`;
      link.style.display = "none";

      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);

      // Clean up the blob URL
      window.URL.revokeObjectURL(blobUrl);
    } catch (error) {
      console.error("Error downloading meal plan:", error);
      alert(
        "Failed to download meal plan. Please try again or contact support."
      );
    } finally {
      setIsDownloading(false);
    }
  };

  useEffect(() => {
    setTitle("Fertility Diet Plan");
    setSubtitle("Let's Personalize Your Diet Plan");
  }, [setTitle, setSubtitle]);

  if (isLoading) {
    return <PageLoader />;
  }

  if (error) {
    return (
      <div className="w-full h-full flex items-center justify-center">
        <Card className="w-[90%] xl:w-[60%] py-10 px-5 md:px-10 md:py-12.5 xl:py-10 xl:px-15 flex flex-col items-center gap-8 justify-center shadow-none border-none rounded-md bg-white relative diet-bg">
          <p className="text-red-500">
            {error instanceof Error
              ? error.message
              : "An unknown error occurred"}
          </p>
        </Card>
      </div>
    );
  }

  // Determine the recommended calories to display
  const recommendedCalories =
    mealPlan?.recommendedTargetCalories || targetCalories || bmr;

  // Determine the condition note to display
  const conditionNote =
    mealPlan?.nutritionalAdvice ||
    "Based on your profile, we'll focus on foods that optimize fertility and support your reproductive health. Our nutrition team can provide personalized guidance.";

  return (
    <div className="w-full h-full flex items-center justify-center">
      <Card className="w-[90%] xl:w-[60%] py-10 px-5 md:px-10 md:py-12.5 xl:py-10 xl:px-15 flex flex-col items-center gap-8 justify-center shadow-none border-none rounded-md bg-white relative diet-bg">
        {/* Header */}
        <h1 className="text-xl md:text-2xl font-bold text-[var(--grey-7)] text-center">
          BMR & Target Calories
        </h1>

        {/* BMR Circle */}
        <div className="flex justify-center w-full">
          <div className="relative">
            <div className="w-47.5 md:w-58 h-47.5 md:h-58 bmr-bg rounded-full flex flex-col items-center justify-center text-white">
              <div className="text-sm font-medium mb-1">Your BMR</div>
              <div className="text-4xl font-bold">{bmr?.toFixed(2)}</div>
              <div className="text-sm opacity-90">kcal/day</div>
            </div>
          </div>
        </div>

        {/* Daily Calorie Target Section */}
        <div className="flex flex-col gap-1 w-full">
          <h2 className="text-xl font-bold text-[var(--grey-7)]">
            Your Daily Calorie Target
          </h2>
          <p className="text-base font-medium text-[var(--grey-6)]">
            Based on your age, weight, and height, your Basal Metabolic Rate
            (BMR) is calculated.
          </p>
        </div>

        {/* BMR Info Box */}
        <div className="flex flex-col xl:flex-row gap-5 xl:gap-15 w-full">
          <div className="w-full flex bg-[var(--violet-1)] px-7.5 md:px-10 py-5">
            <div className="flex items-center gap-3">
              <Image
                src="/assets/diet-plan/Bmr.svg"
                alt="BMR Icon"
                width={31}
                height={31}
              />
              <div>
                <div className="text-base font-medium text-[var(--grey-6)]">
                  Your BMR is:
                </div>
                <div className="text-xl font-bold text-[var(--grey-7)]">
                  {bmr?.toFixed(2)} kcal/day
                </div>
              </div>
            </div>
          </div>

          {/* IVF Target Box */}
          <div className="w-full flex bg-[var(--red-1)] px-7.5 md:px-10 py-5">
            <div className="flex items-center gap-3">
              <Image
                src="/assets/diet-plan/Fire.svg"
                alt="Target Icon"
                width={31}
                height={31}
              />
              <div>
                <div className="text-base font-medium text-[var(--grey-6)]">
                  Recommended Target Calories for IVF:
                </div>
                <div className="text-xl font-bold text-[var(--grey-7)]">
                  {recommendedCalories?.toFixed(2)} kcal/day
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Condition Note */}

        <div className="w-full flex flex-col items-end gap-4 md:gap-3.75 p-5 md:py-5 md:px-7.5 bg-[var(--grey-1)]">
          <div className="w-full flex flex-col gap-2 md:gap-3.25">
            <h3 className="text-xl font-bold text-[var(--grey-7)]">
              Condition Note
            </h3>
            <p className="text-base font-medium text-[var(--grey-6)]">
              {conditionNote}
            </p>
          </div>
          <div className="w-full flex justify-end">
            <Button
              type={ButtonType.SECONDARY}
              text={isDeleting ? "Clearing..." : "Retake Diet Assessment"}
              onClick={handleRetakeAssessmentClick}
              disabled={isDeleting}
              size="default"
              className="!w-[90%] md:!w-[40%] !py-3.25 !px-7.5 border-1 border-[var(--violet-11)] text-[var(--violet-11)]"
              icon={isDeleting && <Loader className="animate-spin mr-2" />}
            ></Button>
          </div>
        </div>

        {/* Action Buttons */}
        <div className="w-full flex flex-col md:flex-row md:justify-between gap-6 md:gap-26.5">
          <Button
            type={ButtonType.SECONDARY}
            text={isDownloading ? "Downloading..." : "Download My 7-Days Plan"}
            icon={
              isDownloading ? (
                <Loader className="animate-spin w-6 h-6" />
              ) : (
                <DownloadSimpleIcon className="w-6 h-6" />
              )
            }
            onClick={handleDownloadConfirmation}
            disabled={isDownloading}
            size="default"
            className="flex flex-row-reverse text-[var(--grey-7)]"
          />
          <Button
            type={ButtonType.PRIMARY}
            text="Book Nutrition Consult"
            onClick={() => setModalOpen(true)}
            size="default"
          />
        </div>
      </Card>
      <Modal
        open={modalOpen}
        title="Consultation Request Submitted"
        message="Thank you for requesting a session with our Nutrition Consultant.\nOur care team will contact you shortly to guide you through the next steps.\nFor urgent queries, please reach out to us at +91-XXXXXXXXXX."
        onClose={() => setModalOpen(false)}
      />
      {showWarningAlert && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
          <div className="w-[90%] max-w-md">
            <WarningAlert
              title="Warning"
              description="Are you sure you want to retake the assessment? This will clear your current assessment and start a new one."
              buttons={[
                {
                  text: "Cancel",
                  type: ButtonType.SECONDARY,
                  onClick: handleWarningCancel,
                },
                {
                  text: "Proceed",
                  type: ButtonType.PRIMARY,
                  onClick: handleWarningProceed,
                },
              ]}
            />
          </div>
        </div>
      )}
      {showDownloadConfirmation && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
          <div className="w-[90%] max-w-md">
            <WarningAlert
              title="Confirmation"
              description="Your file is ready — proceed?"
              buttons={[
                {
                  text: "No",
                  type: ButtonType.SECONDARY,
                  onClick: handleDownloadCancel,
                },
                {
                  text: "Yes",
                  type: ButtonType.PRIMARY,
                  onClick: handleDownloadProceed,
                },
              ]}
            />
          </div>
        </div>
      )}
    </div>
  );
};

export default BMRCalculation;
