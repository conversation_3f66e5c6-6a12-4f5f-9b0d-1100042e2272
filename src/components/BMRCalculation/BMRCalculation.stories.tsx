import React from "react";
import BMRCalculation from "./BMRCalculation";
import { PageHeaderProvider } from "@/contexts/PageHeaderContext";
import { AuthProvider } from "@/contexts/AuthContext";
import { ToastProvider } from "@/contexts/ToastContext";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";

const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      retry: false,
      staleTime: Infinity,
    },
  },
});

export default {
  title: "Components/BMRCalculation",
  component: BMRCalculation,
  decorators: [
    (Story: React.ComponentType) => (
      <QueryClientProvider client={queryClient}>
        <ToastProvider>
          <AuthProvider>
            <PageHeaderProvider>
              <Story />
            </PageHeaderProvider>
          </AuthProvider>
        </ToastProvider>
      </QueryClientProvider>
    ),
  ],
};

export const Default = () => (
  <div
    style={{
      minHeight: 600,
      display: "flex",
      alignItems: "center",
      justifyContent: "center",
      background: "#F7F7F9",
    }}
  >
    <BMRCalculation />
  </div>
);
