import { useQuery } from "@tanstack/react-query";
import { TimeSlot, DateSlotGroup, TimeSlotsResponse } from "@/types/api/public/api-timeslots";

export interface UseTimeSlotsResult {
  dateRange: {
    startDate: string;
    endDate: string;
  };
  totalSlots: number;
  dateSlots: DateSlotGroup[];
  loading: boolean;
  error: string | null;
}

export const useTimeSlots = (
  doctorId: number | string,
  startDate?: string,
  endDate?: string
): UseTimeSlotsResult => {
  const { data, isLoading, error } = useQuery({
    queryKey: ['timeSlots', doctorId, startDate, endDate],
    queryFn: async () => {
      const params = new URLSearchParams({
        doctor_id: doctorId.toString(),
      });

      // Default to next 7 days if no dates provided
      const start = startDate || new Date().toISOString().split('T')[0];
      const end = endDate || new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString().split('T')[0];

      params.append('start_date', start);
      params.append('end_date', end);

      const response = await fetch(`/api/v1/public/time-slots?${params}`);
      if (!response.ok) {
        throw new Error('Failed to fetch available time slots');
      }
      const result = await response.json();
      if (!result.success) {
        throw new Error(result.error || 'Failed to fetch available time slots');
      }
      return result.data;
    },
    enabled: !!doctorId,
    staleTime: 5 * 60 * 1000, // 5 minutes (templates change less frequently)
    gcTime: 10 * 60 * 1000, // 10 minutes
  });

  return {
    dateRange: {
      startDate: data?.date_range?.start_date || startDate || '',
      endDate: data?.date_range?.end_date || endDate || '',
    },
    totalSlots: data?.total_slots || 0,
    dateSlots: data?.date_slots || [],
    loading: isLoading,
    error: error ? (error as Error).message : null,
  };
};

export default useTimeSlots;
