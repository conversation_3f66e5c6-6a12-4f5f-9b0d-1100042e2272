import { useQuery } from "@tanstack/react-query";

type ClinicType = {
  id: number;
  clinic_name: string;
  address?: string;
  contact_info?: string;
};

export const useGetClinic = (centerId: number): string => {
  const { data } = useQuery({
    queryKey: ['all-clinics'],
    queryFn: async () => {
      // First get all cities
      const citiesResponse = await fetch('/api/v1/public/cities');
      if (!citiesResponse.ok) {
        throw new Error('Failed to fetch cities');
      }
      const citiesResult = await citiesResponse.json();
      if (!citiesResult.success) {
        throw new Error(citiesResult.error || 'Failed to fetch cities');
      }

      // Then get all clinics for all cities
      const allClinics = [];
      for (const city of citiesResult.data) {
        const clinicsResponse = await fetch(`/api/v1/public/clinics?city_id=${city.id}`);
        if (clinicsResponse.ok) {
          const clinicsResult = await clinicsResponse.json();
          if (clinicsResult.success) {
            allClinics.push(...clinicsResult.data);
          }
        }
      }
      return allClinics;
    },
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
  });

  if (!data) {
    return "";
  }

  if (!centerId) {
    return "";
  }
  const clinic = data.find((clinic: ClinicType) => clinic.id === centerId);
  return clinic?.clinic_name || "";
};

export default useGetClinic;
